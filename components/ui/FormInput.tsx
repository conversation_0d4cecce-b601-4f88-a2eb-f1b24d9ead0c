import { getCommonStyles } from "@/constants/styles";
import { useTheme } from "@/context/ThemeContext";
import { MaterialIcons } from "@expo/vector-icons";
import { Pressable, Text, TextInput, View } from "react-native";

const FormInput = ({
   icon,
   placeholder,
   value,
   name,
   error,
   onChange,
   secureTextEntry,
   keyboardType,
   autoCapitalize,
   autoComplete,
   textContentType,
   autoCorrect,
   maxLength,
   rightIcon,
   onRightIconPress,
   onFocus,
   onBlur,
}: {
   icon: string;
   placeholder: string;
   value: string;
   name: string;
   error?: string | null;
   onChange: (name: string, value: string) => void;
   colorScheme: "light" | "dark";
   secureTextEntry?: boolean;
   keyboardType?: any;
   autoCapitalize?: any;
   autoComplete?: any;
   textContentType?: any;
   autoCorrect?: boolean;
   maxLength?: number;
   rightIcon?: string;
   onRightIconPress?: () => void;
   onFocus?: () => void;
   onBlur?: () => void;
}) => {
   const { colors } = useTheme();
   const commonStyles = getCommonStyles(colors);

   return (
      <>
         <View
            style={[
               commonStyles.inputGroup,
               error && { borderColor: colors.error, borderWidth: 1.2 },
            ]}
         >
            <MaterialIcons
               name={icon as any}
               size={20}
               color={colors.icon}
               style={commonStyles.inputIcon}
            />
            <TextInput
               style={commonStyles.input}
               placeholder={placeholder}
               placeholderTextColor={colors.icon}
               value={value}
               onChangeText={(text) => onChange(name, text)}
               secureTextEntry={secureTextEntry}
               keyboardType={keyboardType}
               autoCapitalize={autoCapitalize}
               autoComplete={autoComplete}
               textContentType={textContentType}
               autoCorrect={autoCorrect}
               maxLength={maxLength}
               onFocus={onFocus}
               onBlur={onBlur}
            />
            {rightIcon && (
               <Pressable
                  onPress={onRightIconPress}
                  style={commonStyles.eyeButton}
               >
                  <MaterialIcons
                     name={rightIcon as any}
                     size={20}
                     color={colors.icon}
                  />
               </Pressable>
            )}
         </View>
         {error && <Text style={commonStyles.errorText}>{error}</Text>}
      </>
   );
};

export default FormInput;
