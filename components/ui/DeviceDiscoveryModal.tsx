import React from "react";
import {
   Modal,
   View,
   Text,
   Pressable,
   FlatList,
   StyleSheet,
   SafeAreaView,
} from "react-native";
import { ThemedText } from "@/components/ThemedText";
import { ThemedView } from "@/components/ThemedView";
import { useTheme, ThemeColors } from "@/context/ThemeContext";
import { IHealthDevice } from "@/hooks/useIHealthScan";
import {
   DeviceTypeConfig,
   getDeviceTypeBySDKType,
} from "@/constants/deviceTypes";
import { ActivityIndicator } from "react-native-paper";
import { spacing } from "@/constants/spacing";

interface DeviceDiscoveryModalProps {
   visible: boolean;
   onClose: () => void;
   discoveredDevices: IHealthDevice[];
   isScanning: boolean;
   scanningDeviceType: string | null;
   onDeviceSelect: (device: IHealthDevice) => void;
   onStopScan: () => void;
}

export const DeviceDiscoveryModal: React.FC<DeviceDiscoveryModalProps> = ({
   visible,
   onClose,
   discoveredDevices,
   isScanning,
   scanningDeviceType,
   onDeviceSelect,
   onStopScan,
}) => {
   const { colors } = useTheme();
   const styles = getStyles(colors);

   const deviceConfig = scanningDeviceType
      ? getDeviceTypeBySDKType(scanningDeviceType)
      : null;

   const getSignalStrength = (rssi?: number) => {
      if (!rssi) return "📶";
      if (rssi > -50) return "📶";
      if (rssi > -70) return "📶";
      if (rssi > -80) return "📶";
      return "📶";
   };

   const renderDevice = ({ item }: { item: IHealthDevice }) => {
      const deviceName = item.name || `${item.type} Device`;

      return (
         <Pressable
            style={({ pressed }) => [
               styles.deviceItem,
               pressed && styles.deviceItemPressed,
            ]}
            onPress={() => onDeviceSelect(item)}
         >
            <View style={styles.deviceContent}>
               {/* Device Icon */}
               <View style={styles.deviceIconContainer}>
                  <Text style={styles.deviceIcon}>
                     {deviceConfig?.icon || "📱"}
                  </Text>
               </View>

               {/* Device Info */}
               <View style={styles.deviceInfo}>
                  <ThemedText style={styles.deviceName}>
                     {deviceName}
                  </ThemedText>

                  <View style={styles.deviceDetails}>
                     <ThemedText style={styles.deviceDetailText}>
                        {getSignalStrength(item.rssi)} {item.rssi || "N/A"} dBm
                     </ThemedText>
                     <ThemedText style={styles.deviceDetailText}>
                        • {item.type}
                     </ThemedText>
                  </View>

                  <ThemedText style={styles.deviceMac}>{item.mac}</ThemedText>
               </View>

               {/* Connect Button */}
               <View style={styles.connectButtonContainer}>
                  <Text style={styles.connectIcon}>▶</Text>
               </View>
            </View>
         </Pressable>
      );
   };

   const renderHeader = () => (
      <View style={styles.header}>
         <ThemedText style={styles.title}>
            {isScanning ? "Scanning" : "Discovered Devices"}
         </ThemedText>

         {deviceConfig && (
            <View style={styles.deviceTypeInfo}>
               {/* <Text style={styles.deviceTypeIcon}>{deviceConfig.icon}</Text> */}
               <ThemedText style={styles.deviceTypeName}>
                  {deviceConfig.name}
               </ThemedText>
            </View>
         )}

         {/* {isScanning && (
            <View style={styles.scanningIndicator}>
               <Text style={styles.scanningSpinner}>⟳</Text>
               <ThemedText style={styles.scanningText}>
                  Looking for {deviceConfig?.name || "devices"}...
               </ThemedText>
            </View>
         )} */}
      </View>
   );

   const renderEmptyState = () => (
      <View style={styles.emptyState}>
         {/* <Text style={styles.emptyIcon}>🔍</Text> */}
         {isScanning && (
            <ActivityIndicator size={96} animating={true} color={colors.text} />
         )}
         <ThemedText style={[styles.emptyTitle, { marginTop: spacing.xl }]}>
            {isScanning ? "Searching for nearby devices" : "No Devices Found"}
         </ThemedText>
         <ThemedText style={styles.emptyDescription}>
            {isScanning
               ? `Make sure your ${deviceConfig?.name || "device"} is turned on and in pairing mode.`
               : `No ${deviceConfig?.name || "devices"} were discovered. Try scanning again.`}
         </ThemedText>
      </View>
   );

   return (
      <Modal
         visible={visible}
         animationType="slide"
         presentationStyle="pageSheet"
         onRequestClose={onClose}
      >
         <SafeAreaView style={styles.container}>
            <ThemedView style={styles.content}>
               {/* Header with close button */}
               <View style={styles.modalHeader}>
                  <Pressable style={styles.closeButton} onPress={onClose}>
                     <Text style={styles.closeButtonText}>✕</Text>
                  </Pressable>
               </View>

               {/* Device List */}
               <FlatList
                  data={discoveredDevices}
                  renderItem={renderDevice}
                  keyExtractor={(item) => item.mac}
                  ListHeaderComponent={renderHeader}
                  ListEmptyComponent={renderEmptyState}
                  contentContainerStyle={styles.listContent}
                  showsVerticalScrollIndicator={false}
               />

               {/* Action Buttons */}
               <View style={styles.actionButtons}>
                  {isScanning && (
                     <Pressable
                        style={[styles.button, styles.stopButton]}
                        onPress={onStopScan}
                     >
                        <ThemedText style={styles.stopButtonText}>
                           Stop Scanning
                        </ThemedText>
                     </Pressable>
                  )}
               </View>
            </ThemedView>
         </SafeAreaView>
      </Modal>
   );
};

const getStyles = (colors: ThemeColors) =>
   StyleSheet.create({
      container: {
         flex: 1,
         backgroundColor: colors.background,
      },
      content: {
         flex: 1,
         backgroundColor: "transparent",
      },
      modalHeader: {
         flexDirection: "row",
         justifyContent: "flex-end",
         alignItems: "center",
         paddingHorizontal: 20,
         paddingVertical: 10,
      },
      closeButton: {
         width: 40,
         height: 40,
         borderRadius: 20,
         backgroundColor: colors.inputBackground,
         justifyContent: "center",
         alignItems: "center",
      },
      closeButtonText: {
         fontSize: 18,
         color: colors.text,
      },
      listContent: {
         paddingHorizontal: 20,
         paddingBottom: 20,
      },
      header: {
         marginBottom: 20,
         alignItems: "center",
      },
      title: {
         fontSize: 24,
         fontWeight: "bold",
         textAlign: "center",
         marginBottom: 16,
      },
      deviceTypeInfo: {
         flexDirection: "row",
         alignItems: "center",
         marginBottom: 16,
      },
      deviceTypeIcon: {
         fontSize: 24,
         marginRight: 8,
      },
      deviceTypeName: {
         fontSize: 16,
         fontWeight: "600",
      },
      scanningIndicator: {
         flexDirection: "row",
         alignItems: "center",
         backgroundColor: colors.inputBackground,
         paddingHorizontal: 16,
         paddingVertical: 12,
         borderRadius: 20,
      },
      scanningSpinner: {
         fontSize: 16,
         marginRight: 8,
         // Add rotation animation here if needed
      },
      scanningText: {
         fontSize: 14,
         color: colors.icon,
      },
      deviceItem: {
         backgroundColor: colors.inputBackground,
         borderRadius: 12,
         marginBottom: 12,
         borderWidth: 1,
         borderColor: colors.border || "#e0e0e0",
      },
      deviceItemPressed: {
         transform: [{ scale: 0.98 }],
         opacity: 0.8,
      },
      deviceContent: {
         flexDirection: "row",
         alignItems: "center",
         padding: 16,
      },
      deviceIconContainer: {
         marginRight: 12,
      },
      deviceIcon: {
         fontSize: 32,
      },
      deviceInfo: {
         flex: 1,
      },
      deviceName: {
         fontSize: 16,
         fontWeight: "bold",
         marginBottom: 4,
      },
      deviceDetails: {
         flexDirection: "row",
         alignItems: "center",
         marginBottom: 4,
      },
      deviceDetailText: {
         fontSize: 12,
         color: colors.icon,
         marginRight: 8,
      },
      deviceMac: {
         fontSize: 11,
         color: colors.icon,
         fontFamily: "monospace",
      },
      connectButtonContainer: {
         justifyContent: "center",
         alignItems: "center",
         width: 30,
      },
      connectIcon: {
         fontSize: 16,
         color: colors.icon,
      },
      emptyState: {
         alignItems: "center",
         paddingVertical: 40,
      },
      emptyIcon: {
         fontSize: 48,
         marginBottom: 16,
      },
      emptyTitle: {
         fontSize: 18,
         fontWeight: "bold",
         marginBottom: 8,
         textAlign: "center",
      },
      emptyDescription: {
         fontSize: 14,
         color: colors.icon,
         textAlign: "center",
         lineHeight: 20,
         paddingHorizontal: 20,
      },
      actionButtons: {
         paddingHorizontal: 20,
         paddingBottom: 20,
      },
      button: {
         paddingVertical: 16,
         borderRadius: 12,
         alignItems: "center",
      },
      stopButton: {
         backgroundColor: "#FF6B6B",
      },
      stopButtonText: {
         color: "white",
         fontSize: 16,
         fontWeight: "bold",
      },
   });
