import React, { useEffect, useState } from "react";
import {
   View,
   Text,
   ScrollView,
   SafeAreaView,
   Alert,
   StyleSheet,
   Pressable,
   TextInput,
} from "react-native";

import { spacing } from "@/constants/spacing";
import { getCommonStyles } from "@/constants/styles";
import HorizontalRule from "@/components/ui/HorizontalRule";
import { Checkbox } from "react-native-paper";
import { useAuth } from "@/context/AuthContext";
import { useLocalSearchParams, useRouter } from "expo-router";
import { BackendUser } from "@/types/auth";
import { ThemeColors, useTheme } from "@/context/ThemeContext";
import useApi from "@/hooks/useApi";

interface Reading {
   [key: string]: number | string | undefined;
   systolic: number;
   diastolic: number;
   pulse: number;
   timestamp: string;
}

interface SampleTags {
   [key: string]: boolean;
   coughing: boolean;
   headache: boolean;
}

interface ReadingResponse {
   id: number;
   user: BackendUser;
   details: { deviceReading: any; tags: any; additionalNotes: string };
}

const mockReading = {
   systolic: 120,
   diastolic: 80,
   pulse: 60,
   timestamp: new Date().toISOString(),
} as Reading;

const ReadingScreen: React.FC = () => {
   const { colors } = useTheme();
   const { accessToken } = useAuth();
   const commonStyles = getCommonStyles(colors);
   const styles = getStyles(colors);
   const router = useRouter();
   const { get, post } = useApi();

   const params = useLocalSearchParams<{ id?: string }>();

   const [readingData, setReadingData] = useState<Reading | null>(null);
   const [tags, setTags] = useState<SampleTags>({
      coughing: false,
      headache: false,
   });
   const [additionalNotes, setAdditionalNotes] = useState<string>("");
   const [isSaving, setIsSaving] = useState(false);

   useEffect(() => {
      const fetchData = async () => {
         if (params.id && accessToken) {
            try {
               const response = await get<ReadingResponse>(
                  `/readings/${params.id}`,
                  { token: accessToken },
               );
               setReadingData(response.details.deviceReading as Reading);
               setTags(response.details.tags);
               setAdditionalNotes(response.details.additionalNotes);
            } catch (error) {
               console.error("Failed to fetch reading:", error);
               setReadingData(mockReading);
            }
         } else {
            setReadingData(mockReading);
         }
      };

      fetchData();
   }, [params.id, accessToken]);

   const handleSave = async () => {
      setIsSaving(true);

      if (accessToken) {
         const reading = {
            // deviceId: params.deviceId, // link to the selected device
            details: {
               deviceReading: { ...mockReading },
               tags: { ...tags },
               additionalNotes,
            },
         };

         await post("/readings", {
            data: reading,
            token: accessToken,
         });
         router.push("/(app)/submitted"); // forwards to submission screen
      }

      setIsSaving(false);
   };

   // alert box for confirming submission
   const handleSubmitWithConfirmation = () => {
      Alert.alert(
         "Submit Reading",
         "Are you sure you want to submit this reading?",
         [
            { text: "Cancel", style: "cancel" },
            {
               text: "Confirm",
               onPress: () => handleSave(),
            },
         ],
      );
   };

   const formatTimestamp = (isoTimestamp: string) => {
      if (!isoTimestamp) return "N/A";
      try {
         const date = new Date(isoTimestamp);
         return `${date.toLocaleDateString()} ${date.toLocaleTimeString([], {
            hour: "2-digit",
            minute: "2-digit",
         })}`;
      } catch (error) {
         console.error("Error formatting timestamp:", error);
         return "Invalid Date";
      }
   };

   const currentReading = readingData || mockReading;

   return (
      <View>
         <ScrollView contentContainerStyle={styles.scrollContainer}>
            <View style={styles.container}>
               <Text style={styles.title}>
                  {params.id ? "Reading Details" : "New Reading Review"}
               </Text>

               <View style={styles.box}>
                  <Text style={styles.boxHeader}>Device Reading</Text>
                  <HorizontalRule marginBottom={spacing.lg} />
                  {currentReading && (
                     <>
                        <View style={styles.itemGroup}>
                           <Text style={styles.itemHeader}>Systolic</Text>
                           <Text style={styles.itemDetail}>
                              {currentReading.systolic}
                           </Text>
                        </View>
                        <View style={styles.itemGroup}>
                           <Text style={styles.itemHeader}>Diastolic</Text>
                           <Text style={styles.itemDetail}>
                              {currentReading.diastolic}
                           </Text>
                        </View>
                        <View style={styles.itemGroup}>
                           <Text style={styles.itemHeader}>Pulse</Text>
                           <Text style={styles.itemDetail}>
                              {currentReading.pulse}
                           </Text>
                        </View>
                        <View style={styles.itemGroup}>
                           <Text style={styles.itemHeader}>Timestamp</Text>
                           <Text style={styles.itemDetailTimestamp}>
                              {formatTimestamp(currentReading.timestamp)}
                           </Text>
                        </View>
                     </>
                  )}
               </View>

               <View style={styles.box}>
                  <Text style={styles.boxHeader}>Symptoms</Text>
                  <HorizontalRule marginBottom={spacing.md} />
                  {Object.keys(tags).map((key) => (
                     <View key={key} style={styles.symptom}>
                        <Text style={styles.itemHeader}>
                           {key.charAt(0).toUpperCase() + key.slice(1)}
                        </Text>
                        <Checkbox
                           status={tags[key] ? "checked" : "unchecked"}
                           disabled={params.id ? true : false}
                           onPress={() => {
                              const newTags = { ...tags };
                              newTags[key] = !tags[key];
                              setTags(newTags);
                           }}
                        />
                     </View>
                  ))}
               </View>

               <View style={styles.box}>
                  <Text style={styles.boxHeader}>Additional Notes</Text>
                  <HorizontalRule marginBottom={spacing.md} />
                  <TextInput
                     style={styles.noteBox}
                     placeholder="Enter any other observations here"
                     placeholderTextColor={colors.textMuted}
                     value={additionalNotes}
                     onChangeText={setAdditionalNotes}
                     multiline
                  />
               </View>
            </View>
            {!params.id && (
               <View style={styles.footer}>
                  <Pressable
                     style={({ pressed }) => [
                        commonStyles.button,
                        pressed && commonStyles.buttonPressed,
                        isSaving && commonStyles.buttonDisabled,
                     ]}
                     onPress={handleSubmitWithConfirmation} // for the submission screen to appear and the alert box
                     disabled={isSaving}
                  >
                     <Text style={commonStyles.buttonText}>
                        {isSaving ? "Saving..." : "Save"}
                     </Text>
                  </Pressable>
               </View>
            )}
         </ScrollView>
      </View>
   );
};

const getStyles = (colors: ThemeColors) =>
   StyleSheet.create({
      safeArea: {
         flex: 1,
         backgroundColor: colors.background,
      },
      boxHeader: {
         fontSize: 20,
         fontWeight: "bold",
         color: colors.text,
         marginBottom: spacing.md,
      },
      itemGroup: {
         marginBottom: spacing.lg,
         paddingHorizontal: spacing.sm,
      },
      itemHeader: {
         fontSize: 16,
         color: colors.textMuted,
         marginBottom: spacing.xs,
      },
      itemDetail: {
         fontSize: 28,
         fontWeight: "600",
         color: colors.text,
      },
      itemDetailTimestamp: {
         fontSize: 18,
         fontWeight: "500",
         color: colors.text,
         marginTop: spacing.xs,
      },
      symptom: {
         display: "flex",
         flexDirection: "row",
         alignItems: "center",
         justifyContent: "space-between",
         paddingVertical: spacing.sm,
      },
      box: {
         backgroundColor: colors.card,
         borderRadius: 16,
         padding: spacing.lg,
         shadowColor: "#000",
         shadowOffset: { width: 0, height: 2 },
         shadowOpacity: 0.25,
         shadowRadius: 8,
         elevation: 5,
         marginBottom: spacing.xl,
      },
      noteBox: {
         color: colors.text,
         fontSize: 16,
         minHeight: 120,
         textAlignVertical: "top",
         backgroundColor: colors.inputBackground,
         borderRadius: 8,
         padding: spacing.md,
         borderWidth: 1,
         borderColor: colors.border,
      },
      scrollContainer: {
         flexGrow: 1,
         paddingBottom: spacing.lg,
      },
      container: {
         flex: 1,
         backgroundColor: colors.background,
         paddingHorizontal: spacing.lg,
         paddingTop: spacing.lg,
      },
      title: {
         fontSize: 32,
         fontWeight: "bold",
         color: colors.text,
         textAlign: "left",
         marginBottom: spacing.xl,
      },
      readingsContainer: {
         gap: spacing.md,
      },
      footer: {
         paddingHorizontal: spacing.lg,
         paddingVertical: spacing.md,
      },
   });

export default ReadingScreen;
