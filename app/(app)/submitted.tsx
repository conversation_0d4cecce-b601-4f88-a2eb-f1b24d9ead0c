import React, { useEffect } from "react";
import { View, Text, StyleSheet } from "react-native";
import { useRouter } from "expo-router";
import { AntDesign } from "@expo/vector-icons";
import Animated, {
   useSharedValue,
   useAnimatedStyle,
   withTiming,
   withDelay,
   Easing,
} from "react-native-reanimated";

const SubmittedScreen = () => {
   const router = useRouter();
   const scale = useSharedValue(0);
   const opacity = useSharedValue(0);

   useEffect(() => {
      // Animate checkmark
      scale.value = withTiming(1, {
         duration: 600,
         easing: Easing.out(Easing.exp),
      });
      opacity.value = withDelay(400, withTiming(1, { duration: 500 }));

      // Navigate after 2s
      const timeout = setTimeout(() => {
         router.replace("/(app)/devices");
      }, 2000);

      return () => clearTimeout(timeout);
   }, []);

   const iconStyle = useAnimatedStyle(() => ({
      transform: [{ scale: scale.value }],
   }));

   const textStyle = useAnimatedStyle(() => ({
      opacity: opacity.value,
   }));

   return (
      <View style={styles.container}>
         <Animated.View style={iconStyle}>
            <AntDesign name="checkcircle" size={80} color="green" />
         </Animated.View>
         <Animated.Text style={[styles.text, textStyle]}>
            Reading Submitted!
         </Animated.Text>
      </View>
   );
};

const styles = StyleSheet.create({
   container: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      backgroundColor: "#fff",
   },
   text: {
      fontSize: 22,
      fontWeight: "bold",
      marginTop: 20,
      color: "#333",
   },
});

export default SubmittedScreen;
