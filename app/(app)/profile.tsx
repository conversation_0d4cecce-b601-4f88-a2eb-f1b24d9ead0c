import React from "react";
import { View, Text, Pressable, SafeAreaView } from "react-native";
import { useRouter } from "expo-router";
import Ionicons from "@expo/vector-icons/Ionicons";
import MaterialIcons from "@expo/vector-icons/MaterialIcons";

import { useAuth } from "@/context/AuthContext";
import { useTheme } from "@/context/ThemeContext";
import { getCommonStyles } from "@/constants/styles";

const Profile: React.FC = () => {
   const { colors } = useTheme();
   const { user, signOut } = useAuth();
   const router = useRouter();

   const styles = getCommonStyles(colors);

   return (
      <View>
         <View style={styles.avatarContainer}>
            <View style={styles.profileCircle}>
               <Text style={styles.profileInitials}>
                  {user?.firstName?.[0]?.toUpperCase() ?? "U"}
               </Text>
            </View>
            <Text style={styles.profileName}>{user?.firstName ?? "User"}</Text>
         </View>

         <View style={styles.card}>
            <Pressable
               style={styles.row}
               onPress={() => router.push("/user-details")}
            >
               <View style={styles.iconLeft}>
                  <Ionicons
                     name="person-circle-outline"
                     size={28}
                     color={colors.tint}
                  />
               </View>
               <Text style={styles.rowTitle}>User Details</Text>
               <MaterialIcons
                  name="keyboard-arrow-right"
                  size={28}
                  color={colors.icon}
                  style={styles.arrowRight}
               />
            </Pressable>

            <View style={styles.divider} />

            <Pressable
               style={styles.row}
               onPress={() => router.push("/change-password")}
            >
               <View style={styles.iconLeft}>
                  <Ionicons name="key" size={26} color={colors.tint} />
               </View>
               <Text style={styles.rowTitle}>Change Password</Text>
               <MaterialIcons
                  name="keyboard-arrow-right"
                  size={28}
                  color={colors.icon}
                  style={styles.arrowRightFar}
               />
            </Pressable>

            <View style={styles.divider} />

            <Pressable
               style={styles.logoutRow}
               onPress={async () => {
                  await signOut();
               }}
            >
               <View style={styles.iconLeft}>
                  <MaterialIcons name="logout" size={26} color="#e74c3c" />
               </View>
               <Text style={styles.logoutText}>Logout</Text>
               <MaterialIcons
                  name="keyboard-arrow-right"
                  size={28}
                  color="#e74c3c"
                  style={styles.arrowRightFar}
               />
            </Pressable>
         </View>
      </View>
   );
};

export default Profile;
