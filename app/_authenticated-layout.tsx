import React, { useEffect } from "react";
import { Stack } from "expo-router";
import { Alert, View } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { ActivityIndicator } from "react-native-paper";

import { useAuth } from "@/context/AuthContext";
import { useTheme } from "@/context/ThemeContext";
import { getCommonStyles } from "@/constants/styles";

const AuthenticatedLayout: React.FC = () => {
   const {
      isAuthenticated,
      isLoading,
      isSessionExpiring,
      acknowledgeSessionExpiry,
   } = useAuth();
   const { colors } = useTheme();
   const commonStyles = getCommonStyles(colors);

   useEffect(() => {
      if (isSessionExpiring)
         Alert.alert(
            "Session Expired",
            "Your session has expired. Please log in again",
            [{ text: "OK", onPress: acknowledgeSessionExpiry }],
            { cancelable: false },
         );
   }, [isSessionExpiring]);

   return (
      <SafeAreaView style={commonStyles.safeArea}>
         {isLoading ? (
            <View
               style={{
                  flex: 1,
                  justifyContent: "center",
                  alignItems: "center",
               }}
            >
               <ActivityIndicator size="large" />
            </View>
         ) : (
            <Stack>
               <Stack.Protected guard={isAuthenticated}>
                  <Stack.Screen name="(app)" options={{ headerShown: false }} />
                  <Stack.Screen
                     name="(settings)"
                     options={{ headerShown: false }}
                  />
               </Stack.Protected>
               <Stack.Screen name="(auth)" options={{ headerShown: false }} />
               <Stack.Screen name="+not-found" />
            </Stack>
         )}
      </SafeAreaView>
   );
};

export default AuthenticatedLayout;
