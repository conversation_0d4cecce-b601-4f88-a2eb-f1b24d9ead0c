import { useState, useEffect, useRef } from "react";
import { DeviceEventEmitter } from "react-native";
import { iHealthDeviceManagerModule } from "@ihealth/ihealthlibrary-react-native";
import { IHealthDevice } from "./useIHealthScan";

export type DeviceConnectionStatus =
   | "disconnected"
   | "connecting"
   | "connected"
   | "waiting"
   | "failed";

export interface UseIHealthConnectReturn {
   connectionStatus: DeviceConnectionStatus;
   connectedDevice: IHealthDevice | null;
   connectDevice: (device: IHealthDevice) => void;
   disconnectDevice: (device?: IHealthDevice) => void;
   connectPO3Device: (device: IHealthDevice) => void;
}

export const useIHealthConnect = (): UseIHealthConnectReturn => {
   const [connectionStatus, setConnectionStatus] =
      useState<DeviceConnectionStatus>("waiting");
   const [connectedDevice, setConnectedDevice] = useState<IHealthDevice | null>(
      null,
   );
   const connectionTimeoutRef = useRef<NodeJS.Timeout | number | null>(null);
   const connectingDeviceRef = useRef<IHealthDevice | null>(null);

   useEffect(() => {
      const clearConnectionTimeout = () => {
         if (connectionTimeoutRef.current) {
            clearTimeout(connectionTimeoutRef.current as NodeJS.Timeout);
            connectionTimeoutRef.current = null;
         }
      };

      const handleConnection = (
         event: IHealthDevice,
         status: DeviceConnectionStatus,
      ) => {
         clearConnectionTimeout();
         setConnectionStatus(status);

         if (status === "connected") {
            console.log("Device connected:", event);
            setConnectedDevice(event);
         } else {
            console.warn(`Device status: ${status} for ${event.mac}`);
            setConnectedDevice(null);
         }

         connectingDeviceRef.current = null;
      };

      const connectedListener = DeviceEventEmitter.addListener(
         iHealthDeviceManagerModule.Event_Device_Connected,
         (event: IHealthDevice) => handleConnection(event, "connected"),
      );

      const connectFailListener = DeviceEventEmitter.addListener(
         iHealthDeviceManagerModule.Event_Device_Connect_Failed,
         (event: IHealthDevice) => handleConnection(event, "failed"),
      );

      const disconnectListener = DeviceEventEmitter.addListener(
         iHealthDeviceManagerModule.Event_Device_Disconnect,
         (event: IHealthDevice) => handleConnection(event, "disconnected"),
      );

      return () => {
         connectedListener.remove();
         connectFailListener.remove();
         disconnectListener.remove();
      };
   }, []);

   const disconnectDevice = () => {
      try {
         if (connectedDevice) {
            console.log(
               "Disconnecting from connected device:",
               connectedDevice.mac,
            );
            iHealthDeviceManagerModule.disconnectDevice(
               connectedDevice.mac,
               connectedDevice.type,
            );
            setConnectedDevice(null);
            setConnectionStatus("disconnected");
         } else if (connectingDeviceRef.current) {
            // Fallback: disconnect in-progress connection too
            console.log(
               "Forcing disconnect on connecting device:",
               connectingDeviceRef.current.mac,
            );
            iHealthDeviceManagerModule.disconnectDevice(
               connectingDeviceRef.current.mac,
               connectingDeviceRef.current.type,
            );
            connectingDeviceRef.current = null;
            setConnectionStatus("disconnected");
         } else {
            console.warn("No device to disconnect");
         }
      } catch (error) {
         console.error("Error during disconnect:", error);
      }
   };

   const connectDevice = (device: IHealthDevice) => {
      // Always disconnect first to avoid stuck connections
      disconnectDevice();

      setConnectionStatus("connecting");
      connectingDeviceRef.current = device;

      try {
         const timeout = setTimeout(() => {
            console.warn(`Connection timeout for device ${device.mac}`);
            setConnectionStatus("failed");
            connectingDeviceRef.current = null;
         }, 20000);
         connectionTimeoutRef.current = timeout;

         console.log("Connecting to:", device.mac, device.type);
         iHealthDeviceManagerModule.connectDevice(device.mac, device.type);
      } catch (error) {
         console.error("Failed to initiate connection:", error);
         setConnectionStatus("failed");
         connectingDeviceRef.current = null;
      }
   };

   const connectPO3Device = (device: IHealthDevice) => {
      if (device.type !== "PO3") {
         console.error(`Device is not a PO3 pulse oximeter`);
         return;
      }
      connectDevice(device);
   };

   return {
      connectionStatus,
      connectedDevice,
      connectDevice,
      disconnectDevice,
      connectPO3Device,
   };
};
