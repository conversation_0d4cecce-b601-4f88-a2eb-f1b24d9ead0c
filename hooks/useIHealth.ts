import { useEffect, useRef } from "react";
import { useIHealthPermissions } from "./useIHealthPermissions";
import { useIHealthAuth } from "./useIHealthAuth";
import { useIHealthScan, IHealthDevice } from "./useIHealthScan";
import { useIHealthConnect } from "./useIHealthConnect";
import { useIHealthDeviceData } from "./useIHealthDeviceData";
import { Alert } from "react-native";

export const useIHealth = () => {
   const { permissionStatus, requestPermissions } = useIHealthPermissions();
   const { isAuthenticated, authenticate } = useIHealthAuth();
   const scan = useIHealthScan();
   const connection = useIHealthConnect();
   const deviceData = useIHealthDeviceData();
   const initCalled = useRef(false);

   useEffect(() => {
      if (initCalled.current) return;
      initCalled.current = true;

      const init = async () => {
         const permissionsGranted = await requestPermissions();
         if (permissionsGranted) {
            authenticate();
         } else {
            Alert.alert(
               "Permissions Denied",
               "AdaptIT will not be able to connect to devices without the appropriate permissions. You will still have access to other features of the app.",
            );
            console.warn("Permissions denied. iHealth SDK cannot function.");
         }
      };

      init();
   }, [requestPermissions, authenticate]);

   // 🔍 Scan for a specific device by MAC address
   const scanForDeviceByMac = async (
      mac: string,
      timeoutMs = 10000,
   ): Promise<IHealthDevice | null> => {
      return new Promise((resolve) => {
         const start = Date.now();
         scan.startScan("HS2S Pro");

         const interval = setInterval(() => {
            const found = scan.discoveredDevices.find((d) => d.mac === mac);
            if (found) {
               clearInterval(interval);
               scan.stopScan();
               resolve(found);
            } else if (Date.now() - start >= timeoutMs) {
               clearInterval(interval);
               scan.stopScan();
               resolve(null);
            }
         }, 1000);
      });
   };

   // 🔗 Auto-reconnect using MAC
   const connectToDeviceWithScan = async (mac: string) => {
      let device: IHealthDevice | null = null;

      if (!device) {
         device = await scanForDeviceByMac(mac);
      }

      if (!device) {
         Alert.alert(
            "Device Not Found",
            "Could not locate saved device nearby. Please make sure it's on and in range.",
         );
         return;
      }

      connection.connectDevice(device);
   };

   return {
      permissionStatus,
      isAuthenticated,
      ...scan,
      ...connection,
      connectToDeviceWithScan, // ⬅️ Exposed for ScanScreen use
      measurementStatus: deviceData.measurementStatus,
      currentMeasurement: deviceData.currentMeasurement,
      measurementHistory: deviceData.measurementHistory,
      batteryLevel: deviceData.batteryLevel,
      batteryLastUpdated: deviceData.batteryLastUpdated,
      startMeasurement: deviceData.startMeasurement,
      stopMeasurement: deviceData.stopMeasurement,
      getBatteryLevel: deviceData.getBatteryLevel,
      getHistoryData: deviceData.getHistoryData,
      clearMeasurementHistory: deviceData.clearMeasurementHistory,
   };
};
