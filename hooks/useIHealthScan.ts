import { useState, useEffect, useRef } from "react";
import { DeviceEventEmitter } from "react-native";
import { iHealthDeviceManagerModule } from "@ihealth/ihealthlibrary-react-native";

export interface IHealthDevice {
   mac: string;
   name?: string;
   type: string;
   rssi?: number;
}

export interface UseIHealthScanReturn {
   isScanning: boolean;
   discoveredDevices: IHealthDevice[];
   currentScanDeviceType: string | null;
   startScan: (deviceType: string) => void;
   stopScan: () => void;
   startPO3Scan: () => void; // Keep for backward compatibility
   startDeviceTypeScan: (deviceType: string) => void;
}

export const useIHealthScan = (): UseIHealthScanReturn => {
   const [isScanning, setIsScanning] = useState(false);
   const [discoveredDevices, setDiscoveredDevices] = useState<IHealthDevice[]>(
      [],
   );
   const [currentScanDeviceType, setCurrentScanDeviceType] = useState<
      string | null
   >(null);
   const scanTimeoutRef = useRef<number | null>(null);

   useEffect(() => {
      const scanListener = DeviceEventEmitter.addListener(
         iHealthDeviceManagerModule.Event_Scan_Device,
         (event: IHealthDevice) => {
            setDiscoveredDevices((prevDevices) => {
               const exists = prevDevices.some((d) => d.mac === event.mac);
               if (!exists) {
                  return [...prevDevices, event];
               }
               return prevDevices;
            });
         },
      );

      const scanFinishListener = DeviceEventEmitter.addListener(
         iHealthDeviceManagerModule.Event_Scan_Finish,
         () => {
            if (scanTimeoutRef.current) {
               clearTimeout(scanTimeoutRef.current);
               scanTimeoutRef.current = null;
            }
            setIsScanning(false);
            setCurrentScanDeviceType(null);
         },
      );

      return () => {
         scanListener.remove();
         scanFinishListener.remove();
      };
   }, []);

   const startScan = (deviceType: string) => {
      if (isScanning) {
         stopScan();
         setTimeout(() => startScan(deviceType), 500);
         return;
      }

      if (scanTimeoutRef.current) {
         clearTimeout(scanTimeoutRef.current);
         scanTimeoutRef.current = null;
      }

      setDiscoveredDevices([]);
      setIsScanning(true);
      setCurrentScanDeviceType(deviceType);

      try {
         console.log(`Starting discovery for ${deviceType}`);
         iHealthDeviceManagerModule.startDiscovery(deviceType);

         const timeout = setTimeout(() => {
            stopScan();
         }, 30000);
         scanTimeoutRef.current = timeout;
      } catch (error) {
         console.error("Failed to start device scan:", error);
         setIsScanning(false);
         setCurrentScanDeviceType(null);
      }
   };

   const stopScan = () => {
      if (!isScanning) {
         return;
      }

      if (scanTimeoutRef.current) {
         clearTimeout(scanTimeoutRef.current);
         scanTimeoutRef.current = null;
      }

      try {
         iHealthDeviceManagerModule.stopDiscovery();
      } catch (error) {
         console.error("Failed to stop scan:", error);
      }
      setIsScanning(false);
      setCurrentScanDeviceType(null);
   };

   const startPO3Scan = () => {
      startScan(iHealthDeviceManagerModule.PO3 || "PO3");
   };

   const startDeviceTypeScan = (deviceType: string) => {
      startScan(deviceType);
   };

   return {
      isScanning,
      discoveredDevices,
      currentScanDeviceType,
      startScan,
      stopScan,
      startPO3Scan,
      startDeviceTypeScan,
   };
};
