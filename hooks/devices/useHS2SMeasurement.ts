import { useState, useEffect, useRef } from "react";
import { DeviceEventEmitter } from "react-native";
import {
   HS2SProModule,
   HS2SProfileModule,
} from "@ihealth/ihealthlibrary-react-native";

export interface HS2SMeasurementData {
   weight: number;
   unit?: string;
   bmi?: number;
   bodyFat?: number;
   muscle?: number;
   bone?: number;
   water?: number;
   visceralFat?: number;
   protein?: number;
   physicalAge?: number;
   timestamp: string;
   deviceMac: string;
   deviceType: "HS2S";
}

export type HS2SMeasurementStatus =
   | "idle"
   | "measuring"
   | "completed"
   | "failed"
   | "timeout";

export interface UseHS2SMeasurementReturn {
   measurementStatus: HS2SMeasurementStatus;
   currentMeasurement: HS2SMeasurementData | null;
   measurementHistory: HS2SMeasurementData[];
   batteryLevel: number | null;
   batteryLastUpdated: string | null;
   startMeasurement: (deviceMac: string) => void;
   stopMeasurement: () => void;
   getBatteryLevel: (deviceMac: string) => void;
   getHistoryData: (deviceMac: string) => void;
   clearMeasurementHistory: () => void;
}

export const useHS2SMeasurement = (): UseHS2SMeasurementReturn => {
   const [measurementStatus, setMeasurementStatus] =
      useState<HS2SMeasurementStatus>("idle");
   const [currentMeasurement, setCurrentMeasurement] =
      useState<HS2SMeasurementData | null>(null);
   const [measurementHistory, setMeasurementHistory] = useState<
      HS2SMeasurementData[]
   >([]);
   const [batteryLevel, setBatteryLevel] = useState<number | null>(null);
   const [batteryLastUpdated, setBatteryLastUpdated] = useState<string | null>(
      null,
   );

   const measurementTimeoutRef = useRef<number | null>(null);

   useEffect(() => {
      const handleHS2SNotification = (event: any) => {
         try {
            const eventData =
               typeof event === "string" ? JSON.parse(event) : event;

            console.log(`HS2S Pro EVENT: Received ${eventData.action}`);

            if (eventData.action) {
               switch (eventData.action) {
                  case "action_specify_users":
                     console.log(
                        "HS2S Pro EVENT: User specified, ready for measurement",
                     );
                     setMeasurementStatus("measuring");
                     break;

                  case "action_online_real_time_weight":
                     console.log(
                        "HS2S Pro EVENT: Real-time weight data received",
                     );
                     console.log("HS2S Pro LIVE WEIGHT:", eventData.weight);
                     setMeasurementStatus("measuring");
                     break;

                  case "action_online_result":
                     console.log(
                        "HS2S Pro EVENT: Basic weight measurement completed",
                     );
                     const basicMeasurement: HS2SMeasurementData = {
                        weight: Number(eventData.weight.toFixed(2)) || 0,
                        timestamp: new Date().toISOString(),
                        deviceMac: eventData.mac || "",
                        deviceType: "HS2S",
                     };

                     console.log("HS2S Pro BASIC RESULT:", basicMeasurement);
                     setCurrentMeasurement(basicMeasurement);
                     break;

                  case "action_body_fat_result":
                     console.log(
                        "HS2S Pro EVENT: Body composition analysis completed",
                     );
                     setMeasurementStatus("completed");

                     const bodyFatData = eventData.data_body_fat_result;
                     const fullMeasurement: HS2SMeasurementData = {
                        weight: Number(bodyFatData.weight.toFixed(2)) || 0,
                        bmi: bodyFatData.bmi,
                        bodyFat: bodyFatData.fat_weight,
                        muscle: bodyFatData.muscle_mas,
                        bone: bodyFatData.bone_salt_content,
                        water: bodyFatData.body_water_rate,
                        visceralFat: bodyFatData.visceral_fat_grade,
                        protein: bodyFatData.protein_rate,
                        physicalAge: bodyFatData.physical_age,
                        timestamp: new Date().toISOString(),
                        deviceMac: eventData.mac || "",
                        deviceType: "HS2S",
                     };

                     console.log("HS2S Pro FULL RESULT:", fullMeasurement);

                     setCurrentMeasurement(fullMeasurement);
                     setMeasurementHistory((prev) => [
                        fullMeasurement,
                        ...prev,
                     ]);

                     if (measurementTimeoutRef.current) {
                        clearTimeout(measurementTimeoutRef.current);
                        measurementTimeoutRef.current = null;
                     }
                     break;

                  case "action_measure_finish_at_critical":
                     console.log(
                        "HS2S Pro EVENT: Measurement finished at critical point",
                     );
                     setMeasurementStatus("completed");
                     break;

                  case "action_get_battery_hs":
                     console.log("HS2S Pro EVENT: Battery data received");
                     setBatteryLevel(eventData.battery_hs || null);
                     setBatteryLastUpdated(new Date().toISOString());
                     break;

                  case "action_history_data":
                     console.log("HS2S Pro EVENT: History data received");
                     const historyArray = eventData.history_data;
                     if (historyArray && historyArray.length > 0) {
                        console.log(
                           `HS2S Pro HISTORY: Found ${historyArray.length} stored measurements`,
                        );
                        // Process history data if needed
                     }
                     break;

                  case "action_history_data_num":
                     console.log("HS2S Pro EVENT: History data count received");
                     console.log(
                        `HS2S Pro HISTORY COUNT: ${eventData.history_data_count}`,
                     );
                     break;

                  default:
                     console.log(
                        `HS2S Pro EVENT: Unknown action: ${eventData.action}`,
                     );
                     break;
               }
            }
         } catch (error) {
            console.error("HS2S Pro ERROR: Failed to parse event:", error);
         }
      };

      const hs2sListener = DeviceEventEmitter.addListener(
         HS2SProModule.Event_Notify,
         handleHS2SNotification,
      );

      return () => {
         hs2sListener.remove();
         if (measurementTimeoutRef.current) {
            clearTimeout(measurementTimeoutRef.current);
         }
      };
   }, []);

   const startMeasurement = (deviceMac: string) => {
      console.log(
         `HS2S Pro MEASUREMENT: Starting measurement for device: ${deviceMac}`,
      );

      if (measurementTimeoutRef.current) {
         console.log("HS2S Pro MEASUREMENT: Clearing existing timeout");
         clearTimeout(measurementTimeoutRef.current);
         measurementTimeoutRef.current = null;
      }

      console.log("HS2S Pro MEASUREMENT: Setting status to 'measuring'");
      setMeasurementStatus("measuring");
      setCurrentMeasurement(null);

      try {
         console.log("HS2S Pro MEASUREMENT: Calling HS2SProModule.measure()");
         HS2SProModule.measure(
            deviceMac,
            1,
            "1234567890123456",
            1572317401,
            71,
            20,
            180,
            0,
            1,
            1,
         );
         console.log(
            "HS2S Pro MEASUREMENT: HS2SProModule.measure() call completed successfully",
         );

         console.log("HS2S Pro MEASUREMENT: Setting 120-second timeout");
         const timeout = setTimeout(() => {
            console.log(
               "HS2S Pro MEASUREMENT: Timeout reached - no measurement received",
            );
            setMeasurementStatus("timeout");
         }, 120000); // 2 minutes for scale measurements
         measurementTimeoutRef.current = timeout;

         console.log(
            "HS2S Pro MEASUREMENT: Measurement initiation complete, waiting for device response",
         );
      } catch (error) {
         console.error(
            "HS2S Pro MEASUREMENT ERROR: Failed to start measurement:",
            error,
         );
         setMeasurementStatus("failed");
      }
   };

   const stopMeasurement = () => {
      console.log("HS2S Pro MEASUREMENT: Stopping measurement");
      if (measurementTimeoutRef.current) {
         clearTimeout(measurementTimeoutRef.current);
         measurementTimeoutRef.current = null;
      }
      // HS2S Pro measurements complete naturally
      setMeasurementStatus("idle");
   };

   const getBatteryLevel = (deviceMac: string) => {
      console.log(
         `HS2S Pro BATTERY: Getting battery level for device: ${deviceMac}`,
      );
      try {
         HS2SProModule.getBattery(deviceMac);
         console.log("HS2S Pro BATTERY: getBattery() call completed");
      } catch (error) {
         console.error(
            "HS2S Pro BATTERY ERROR: Failed to get battery level:",
            error,
         );
      }
   };

   const getHistoryData = (deviceMac: string) => {
      console.log(
         `HS2S Pro HISTORY: Getting history data for device: ${deviceMac}`,
      );
      try {
         // For HS2S, we need a user ID to get history data
         // For now, we'll get anonymous data
         HS2SProModule.getAnonymousMemoryData(deviceMac);
         console.log(
            "HS2S Pro HISTORY: getAnonymousMemoryData() call completed",
         );
      } catch (error) {
         console.error(
            "HS2S Pro HISTORY ERROR: Failed to get history data:",
            error,
         );
      }
   };

   const clearMeasurementHistory = () => {
      console.log("HS2S Pro HISTORY: Clearing measurement history");
      setMeasurementHistory([]);
   };

   return {
      measurementStatus,
      currentMeasurement,
      measurementHistory,
      batteryLevel,
      batteryLastUpdated,
      startMeasurement,
      stopMeasurement,
      getBatteryLevel,
      getHistoryData,
      clearMeasurementHistory,
   };
};
