import { useState, useEffect, useRef } from "react";
import { DeviceEventEmitter } from "react-native";
import {
   PO3Module,
   POProfileModule,
} from "@ihealth/ihealthlibrary-react-native";

export interface PO3MeasurementData {
   spo2: number;
   heartRate: number;
   pulseRate: number;
   pi: number; // Perfusion Index
   pulseWave?: number;
   pulseStrength?: number;
   timestamp: string;
   deviceMac: string;
   deviceType: "PO3";
}

export type PO3MeasurementStatus =
   | "idle"
   | "measuring"
   | "completed"
   | "failed"
   | "timeout";

export interface UsePO3MeasurementReturn {
   measurementStatus: PO3MeasurementStatus;
   currentMeasurement: PO3MeasurementData | null;
   measurementHistory: PO3MeasurementData[];
   batteryLevel: number | null;
   batteryLastUpdated: string | null;
   startMeasurement: (deviceMac: string) => void;
   stopMeasurement: () => void;
   getBatteryLevel: (deviceMac: string) => void;
   getHistoryData: (deviceMac: string) => void;
   clearMeasurementHistory: () => void;
}

export const usePO3Measurement = (): UsePO3MeasurementReturn => {
   const [measurementStatus, setMeasurementStatus] =
      useState<PO3MeasurementStatus>("idle");
   const [currentMeasurement, setCurrentMeasurement] =
      useState<PO3MeasurementData | null>(null);
   const [measurementHistory, setMeasurementHistory] = useState<
      PO3MeasurementData[]
   >([]);
   const [batteryLevel, setBatteryLevel] = useState<number | null>(null);
   const [batteryLastUpdated, setBatteryLastUpdated] = useState<string | null>(
      null,
   );

   const measurementTimeoutRef = useRef<number | null>(null);

   useEffect(() => {
      const handlePO3Notification = (event: any) => {
         try {
            const eventData =
               typeof event === "string" ? JSON.parse(event) : event;

            if (eventData.action) {
               switch (eventData.action) {
                  case POProfileModule.ACTION_LIVEDA_PO:
                     setMeasurementStatus("measuring");

                     // Log live data for debugging
                     console.log("PO3 LIVE DATA:", {
                        pulseWave: eventData[POProfileModule.PULSE_WAVE_PO],
                        pi: eventData[POProfileModule.PI_PO],
                        pulseStrength:
                           eventData[POProfileModule.PULSE_STRENGTH_PO],
                        bloodOxygen: eventData[POProfileModule.BLOOD_OXYGEN_PO],
                        pulseRate: eventData[POProfileModule.PULSE_RATE_PO],
                     });
                     break;

                  case POProfileModule.ACTION_RESULTDATA_PO:
                     console.log("PO3: Measurement completed");
                     setMeasurementStatus("completed");

                     const finalMeasurement: PO3MeasurementData = {
                        spo2: eventData[POProfileModule.BLOOD_OXYGEN_PO] || 0,
                        heartRate:
                           eventData[POProfileModule.PULSE_RATE_PO] || 0,
                        pulseRate:
                           eventData[POProfileModule.PULSE_RATE_PO] || 0,
                        pi: parseFloat(
                           (eventData[POProfileModule.PI_PO] || 0).toFixed(2),
                        ),
                        pulseWave: eventData[POProfileModule.PULSE_WAVE_PO],
                        pulseStrength:
                           eventData[POProfileModule.PULSE_STRENGTH_PO],
                        timestamp: new Date().toISOString(),
                        deviceMac: eventData.mac || "",
                        deviceType: "PO3",
                     };

                     setCurrentMeasurement(finalMeasurement);
                     setMeasurementHistory((prev) => [
                        finalMeasurement,
                        ...prev,
                     ]);

                     if (measurementTimeoutRef.current) {
                        clearTimeout(measurementTimeoutRef.current);
                        measurementTimeoutRef.current = null;
                     }
                     break;

                  case POProfileModule.ACTION_BATTERY_PO:
                     console.log("PO3: Battery level updated");
                     setBatteryLevel(
                        eventData[POProfileModule.BATTERY_PO] || null,
                     );
                     setBatteryLastUpdated(new Date().toISOString());
                     break;

                  case POProfileModule.ACTION_ERROR_PO:
                     const errorType =
                        eventData.error_po || eventData.ERROR_PO || "unknown";

                     if (errorType === "disconnect") {
                        console.log("PO3: Device disconnected");
                        setMeasurementStatus("failed");
                     } else {
                        console.error(
                           "PO3 ERROR: Measurement failed:",
                           eventData,
                        );
                        setMeasurementStatus("failed");
                     }

                     if (measurementTimeoutRef.current) {
                        clearTimeout(measurementTimeoutRef.current);
                        measurementTimeoutRef.current = null;
                     }
                     break;

                  case POProfileModule.ACTION_OFFLINEDATA_PO:
                     const dataArray =
                        eventData[POProfileModule.OFFLINEDATA_PO];
                     if (dataArray && dataArray.length > 0) {
                        console.log(
                           `PO3: Found ${dataArray.length} stored measurements`,
                        );
                        // Process offline data if needed
                     }
                     break;

                  case POProfileModule.ACTION_NO_OFFLINEDATA_PO:
                     // No logging needed for this common case
                     break;

                  default:
                     console.log(`PO3: Unknown action: ${eventData.action}`);
                     break;
               }
            }
         } catch (error) {
            console.error("PO3 ERROR: Failed to parse event:", error);
         }
      };

      const po3Listener = DeviceEventEmitter.addListener(
         PO3Module.Event_Notify,
         handlePO3Notification,
      );

      return () => {
         po3Listener.remove();
         if (measurementTimeoutRef.current) {
            clearTimeout(measurementTimeoutRef.current);
         }
      };
   }, []);

   const startMeasurement = (deviceMac: string) => {
      console.log(`PO3: Starting measurement for ${deviceMac}`);

      if (measurementTimeoutRef.current) {
         clearTimeout(measurementTimeoutRef.current);
         measurementTimeoutRef.current = null;
      }

      setMeasurementStatus("measuring");
      setCurrentMeasurement(null);

      try {
         PO3Module.startMeasure(deviceMac);

         const timeout = setTimeout(() => {
            console.log("PO3: Measurement timeout");
            setMeasurementStatus("timeout");
         }, 60000);
         measurementTimeoutRef.current = timeout;
      } catch (error) {
         console.error("PO3 ERROR: Failed to start measurement:", error);
         setMeasurementStatus("failed");
      }
   };

   const stopMeasurement = () => {
      if (measurementTimeoutRef.current) {
         clearTimeout(measurementTimeoutRef.current);
         measurementTimeoutRef.current = null;
      }
      // PO3 doesn't have a stop method - measurements complete naturally
      setMeasurementStatus("idle");
   };

   const getBatteryLevel = (deviceMac: string) => {
      // Add a small delay to ensure any recent disconnect events have been processed
      setTimeout(() => {
         try {
            PO3Module.getBattery(deviceMac);
         } catch (error) {
            console.error("PO3 ERROR: Failed to get battery level:", error);
         }
      }, 500); // 500ms delay to avoid immediate disconnect conflicts
   };

   const getHistoryData = (deviceMac: string) => {
      try {
         PO3Module.getHistoryData(deviceMac);
      } catch (error) {
         console.error("PO3 ERROR: Failed to get history data:", error);
      }
   };

   const clearMeasurementHistory = () => {
      setMeasurementHistory([]);
   };

   return {
      measurementStatus,
      currentMeasurement,
      measurementHistory,
      batteryLevel,
      batteryLastUpdated,
      startMeasurement,
      stopMeasurement,
      getBatteryLevel,
      getHistoryData,
      clearMeasurementHistory,
   };
};
