import { useState, useEffect, useCallback } from "react";
import { DeviceEventEmitter, Platform } from "react-native";
import { iHealthDeviceManagerModule } from "@ihealth/ihealthlibrary-react-native";

export interface UseIHealthAuthReturn {
   isAuthenticated: boolean;
   authenticate: (licenseFile?: string) => void;
}

export const useIHealthAuth = (): UseIHealthAuthReturn => {
   const [isAuthenticated, setIsAuthenticated] = useState(false);

   useEffect(() => {
      const authListener = DeviceEventEmitter.addListener(
         iHealthDeviceManagerModule.Event_Authenticate_Result,
         // TODO: Confirm the return value of "access"
         (event: { access: number | boolean; error?: string }) => {
            if (event.access === 1 || event.access === true) {
               setIsAuthenticated(true);
            } else {
               console.error(
                  `iHealth Auth Failed: ${event.error || "Unknown error"}`,
               );
               setIsAuthenticated(false);
            }
         },
      );

      return () => {
         authListener.remove();
      };
   }, []);

   const authenticate = useCallback((licenseFile?: string) => {
      console.log(`Platform: ${Platform.OS}`);

      try {
         const defaultLicenseFile =
            Platform.OS === "ios"
               ? "com_anonymous_fhirmobilemvp_ios.pem"
               : "com_anonymous_fhirmobilemvp_android.pem";

         const fileToUse = licenseFile || defaultLicenseFile;
         console.log(`Using license file: ${fileToUse}`);

         iHealthDeviceManagerModule.sdkAuthWithLicense(fileToUse);
      } catch (error) {
         console.error("Authentication call failed:", error);
         setIsAuthenticated(false);
      }
   }, []);

   return {
      isAuthenticated,
      authenticate,
   };
};
