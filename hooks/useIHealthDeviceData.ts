import { useState } from "react";
import { usePO3Measurement } from "./devices/usePO3Measurement";
import { useHS2SMeasurement } from "./devices/useHS2SMeasurement";

export interface DeviceMeasurementData {
   // Common fields for all devices
   timestamp: string;
   deviceMac: string;
   deviceType: string;

   // Pulse Oximeter specific
   spo2?: number;
   heartRate?: number;
   pulseRate?: number;
   pi?: number; // Perfusion Index

   // Scale specific
   weight?: number;
   unit?: string;
   bmi?: number;
   bodyFat?: number;
   muscle?: number;
   bone?: number;
   water?: number;

   // Blood pressure specific
   systolic?: number;
   diastolic?: number;
   meanArterialPressure?: number;

   // Battery info (common)
   batteryLevel?: number;
}

export interface DeviceBatteryData {
   batteryLevel: number;
   deviceMac: string;
   deviceType: string;
   timestamp: string;
}

export type DeviceMeasurementStatus =
   | "idle"
   | "measuring"
   | "completed"
   | "failed"
   | "timeout";

export interface UseIHealthDeviceDataReturn {
   // Measurement state
   measurementStatus: DeviceMeasurementStatus;
   currentMeasurement: DeviceMeasurementData | null;
   measurementHistory: DeviceMeasurementData[];

   // Battery state
   batteryLevel: number | null;
   batteryLastUpdated: string | null;

   // Actions
   startMeasurement: (deviceMac: string, deviceType: string) => void;
   stopMeasurement: (deviceType: string) => void;
   getBatteryLevel: (deviceMac: string, deviceType: string) => void;
   getHistoryData: (deviceMac: string, deviceType: string) => void;
   clearMeasurementHistory: () => void;
}

export const useIHealthDeviceData = (): UseIHealthDeviceDataReturn => {
   // Use device-specific hooks
   const po3Data = usePO3Measurement();
   const hs2sData = useHS2SMeasurement();

   // Track current device type for routing
   const [currentDeviceType, setCurrentDeviceType] = useState<string | null>(
      null,
   );

   // Generic state that routes to appropriate device-specific hook
   const getCurrentDeviceData = () => {
      switch (currentDeviceType) {
         case "PO3":
            return {
               measurementStatus: po3Data.measurementStatus,
               currentMeasurement: po3Data.currentMeasurement,
               measurementHistory: po3Data.measurementHistory,
               batteryLevel: po3Data.batteryLevel,
               batteryLastUpdated: po3Data.batteryLastUpdated,
            };
         case "HS2S Pro":
            return {
               measurementStatus: hs2sData.measurementStatus,
               currentMeasurement: hs2sData.currentMeasurement,
               measurementHistory: hs2sData.measurementHistory,
               batteryLevel: hs2sData.batteryLevel,
               batteryLastUpdated: hs2sData.batteryLastUpdated,
            };
         default:
            return {
               measurementStatus: "idle" as DeviceMeasurementStatus,
               currentMeasurement: null,
               measurementHistory: [],
               batteryLevel: null,
               batteryLastUpdated: null,
            };
      }
   };

   const deviceData = getCurrentDeviceData();

   // Device-specific hooks handle their own event listeners
   // No need for generic event handling here

   const startMeasurement = (deviceMac: string, deviceType: string) => {
      console.log(
         `ROUTING: Starting measurement for ${deviceType} device: ${deviceMac}`,
      );
      setCurrentDeviceType(deviceType);

      switch (deviceType) {
         case "PO3":
            po3Data.startMeasurement(deviceMac);
            break;
         case "HS2S Pro":
            hs2sData.startMeasurement(deviceMac);
            break;
         default:
            console.error(
               `ROUTING ERROR: Unsupported device type: ${deviceType}`,
            );
            break;
      }
   };

   const stopMeasurement = (deviceType: string) => {
      console.log(`ROUTING: Stopping measurement for ${deviceType}`);

      switch (deviceType) {
         case "PO3":
            po3Data.stopMeasurement();
            break;
         case "HS2S Pro":
            hs2sData.stopMeasurement();
            break;
         default:
            console.error(
               `ROUTING ERROR: Unsupported device type: ${deviceType}`,
            );
            break;
      }
   };

   const getBatteryLevel = (deviceMac: string, deviceType: string) => {
      console.log(
         `ROUTING: Getting battery level for ${deviceType} device: ${deviceMac}`,
      );

      switch (deviceType) {
         case "PO3":
            po3Data.getBatteryLevel(deviceMac);
            break;
         case "HS2S Pro":
            hs2sData.getBatteryLevel(deviceMac);
            break;
         default:
            console.error(
               `ROUTING ERROR: Unsupported device type: ${deviceType}`,
            );
            break;
      }
   };

   const getHistoryData = (deviceMac: string, deviceType: string) => {
      console.log(
         `ROUTING: Getting history data for ${deviceType} device: ${deviceMac}`,
      );

      switch (deviceType) {
         case "PO3":
            po3Data.getHistoryData(deviceMac);
            break;
         case "HS2S Pro":
            hs2sData.getHistoryData(deviceMac);
            break;
         default:
            console.error(
               `ROUTING ERROR: Unsupported device type: ${deviceType}`,
            );
            break;
      }
   };

   const clearMeasurementHistory = () => {
      console.log(
         `ROUTING: Clearing measurement history for ${currentDeviceType}`,
      );

      switch (currentDeviceType) {
         case "PO3":
            po3Data.clearMeasurementHistory();
            break;
         case "HS2S Pro":
            hs2sData.clearMeasurementHistory();
            break;
         default:
            console.log("ROUTING: No device type selected, nothing to clear");
            break;
      }
   };

   return {
      measurementStatus: deviceData.measurementStatus,
      currentMeasurement: deviceData.currentMeasurement,
      measurementHistory: deviceData.measurementHistory,
      batteryLevel: deviceData.batteryLevel,
      batteryLastUpdated: deviceData.batteryLastUpdated,
      startMeasurement,
      stopMeasurement,
      getBatteryLevel,
      getHistoryData,
      clearMeasurementHistory,
   };
};
