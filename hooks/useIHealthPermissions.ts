import { useState, useEffect, useCallback } from "react";
import { Platform, PermissionsAndroid, Permission } from "react-native";

type PermissionStatus = "granted" | "denied" | "unavailable";

export interface UsePermissionsReturn {
   permissionStatus: PermissionStatus;
   requestPermissions: () => Promise<boolean>;
}

export const useIHealthPermissions = (): UsePermissionsReturn => {
   const [permissionStatus, setPermissionStatus] =
      useState<PermissionStatus>("unavailable");

   const checkPermissions = useCallback(async () => {
      if (Platform.OS === "android") {
         const granted = await PermissionsAndroid.check(
            PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
         );
         setPermissionStatus(granted ? "granted" : "denied");
      } else {
         // For iOS, we don't need to check permissions as they are handled
         // automatically by the OS when the Bluetooth request is made.
         setPermissionStatus("granted");
      }
   }, []);

   useEffect(() => {
      checkPermissions();
   }, [checkPermissions]);

   const requestPermissions = useCallback(async (): Promise<boolean> => {
      if (Platform.OS === "android") {
         try {
            const isAndroid = Platform.OS === "android";
            const version = Platform.Version;

            let permissions: Permission[];

            if (version >= 33) {
               permissions = [
                  PermissionsAndroid.PERMISSIONS.BLUETOOTH_SCAN,
                  PermissionsAndroid.PERMISSIONS.BLUETOOTH_CONNECT,
                  PermissionsAndroid.PERMISSIONS.RECORD_AUDIO,
                  PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
                  PermissionsAndroid.PERMISSIONS.ACCESS_COARSE_LOCATION,
               ];
            } else if (version < 31) {
               permissions = [
                  PermissionsAndroid.PERMISSIONS.BLUETOOTH_SCAN,
                  PermissionsAndroid.PERMISSIONS.BLUETOOTH_CONNECT,
                  PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE,
                  PermissionsAndroid.PERMISSIONS.RECORD_AUDIO,
                  PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
                  PermissionsAndroid.PERMISSIONS.ACCESS_COARSE_LOCATION,
               ];
            } else {
               permissions = [
                  PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE,
                  PermissionsAndroid.PERMISSIONS.RECORD_AUDIO,
                  PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
                  PermissionsAndroid.PERMISSIONS.ACCESS_COARSE_LOCATION,
                  PermissionsAndroid.PERMISSIONS.BLUETOOTH_SCAN,
                  PermissionsAndroid.PERMISSIONS.BLUETOOTH_CONNECT,
               ];
            }

            const granted =
               await PermissionsAndroid.requestMultiple(permissions);
            console.log("Permission results:", granted);

            // Check if all required permissions are granted
            const allGranted = Object.values(granted).every(
               (result) => result === PermissionsAndroid.RESULTS.GRANTED,
            );

            if (allGranted) {
               setPermissionStatus("granted");
               return true;
            } else {
               setPermissionStatus("denied");
               console.warn("Not all permissions granted:", granted);
               return false;
            }
         } catch (err) {
            console.warn("Permission request error:", err);
            return false;
         }
      }
      // For iOS, the permission request is triggered by the first Bluetooth action
      // within the iHealth SDK itself. We don't need to manually trigger it here.
      setPermissionStatus("granted");
      return true;
   }, []);

   return {
      permissionStatus,
      requestPermissions,
   };
};
