import { getItemAsync, setItemAsync } from "expo-secure-store";
import { SessionExpiredError } from "@/utils/SessionExpiredError";

const API_BASE_URL = process.env.EXPO_PUBLIC_API_URL;

export const refreshToken = async () => {
   console.log("Attempting to refresh token");

   const storedRefreshToken = await getItemAsync("refreshToken");
   if (!storedRefreshToken) {
      console.warn("No refresh token found");
      throw new SessionExpiredError("No refresh token found");
   }

   const response = await fetch(`${API_BASE_URL}/users/refresh`, {
      method: "POST",
      headers: { Authorization: `Bearer ${storedRefreshToken}` },
   });

   if (!response.ok) {
      console.warn("Refresh call failed:", await response.text());
      throw new SessionExpiredError("Failed to refresh token");
   }

   const data = await response.json();
   console.log("Token refresh response:", data);

   const accessToken = data.accessToken || data.access_token;

   if (!accessToken) {
      console.warn("Backend did not return accessToken");
      throw new SessionExpiredError("No accessToken in refresh response");
   }

   await setItemAsync("accessToken", String(accessToken));
   console.log("New access token stored");

   if (data.refreshToken || data.refresh_token) {
      const refreshToken = data.refreshToken || data.refresh_token;
      await setItemAsync("refreshToken", String(refreshToken));
      console.log("New refresh token stored");
   }
};
