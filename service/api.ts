import { getItemAsync } from "expo-secure-store";
import { refreshToken } from "./refresh-token";

const API_BASE_URL = process.env.EXPO_PUBLIC_API_URL;

type HTTPMethod = "GET" | "POST" | "PUT" | "PATCH" | "DELETE";

const request = async <TResponse, TRequest = Record<string, any>>(
   method: HTTPMethod,
   endpoint: string,
   retry: boolean,
   data?: TRequest,
   token?: string,
): Promise<TResponse> => {
   try {
      const headers: HeadersInit = { "Content-Type": "application/json" };
      if (token) {
         headers["Authorization"] = `Bearer ${token}`;
      }

      // Make the request
      const response = await fetch(`${API_BASE_URL}${endpoint}`, {
         method,
         headers,
         body: JSON.stringify(data),
      });

      if (response.status === 401 && retry) {
         console.log("Request failed. Attempting to retry...");
         // Refresh the token and make a second attempt
         await refreshToken();
         const newToken = await getItemAsync("accessToken");

         if (newToken) return request(method, endpoint, false, data, newToken);
      }

      if (!response.ok) {
         const errorText = await response.text();
         let errorMessage = errorText;
         const errorJson = JSON.parse(errorMessage);
         errorMessage =
            errorJson.message ||
            errorJson.error ||
            errorJson.detail ||
            errorText;

         console.log(`Response was not OK: ${errorMessage}`);
         throw new Error(
            errorMessage ||
               `HTTP error ${response.status}: ${response.statusText}`,
         );
      }

      const contentType = response.headers.get("content-type");
      if (contentType?.includes("application/json")) {
         return response.json() as Promise<TResponse>;
      } else {
         const textResponse = await response.text();
         return textResponse as TResponse;
      }
   } catch (error) {
      console.warn(`API ${method} Error (${endpoint}):`, error);
      throw error;
   }
};

export { request, HTTPMethod };
