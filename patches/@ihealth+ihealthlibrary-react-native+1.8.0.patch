diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/.classpath b/node_modules/@ihealth/ihealthlibrary-react-native/android/.classpath
new file mode 100644
index 0000000..0a3280e
--- /dev/null
+++ b/node_modules/@ihealth/ihealthlibrary-react-native/android/.classpath
@@ -0,0 +1,6 @@
+<?xml version="1.0" encoding="UTF-8"?>
+<classpath>
+	<classpathentry kind="con" path="org.eclipse.jdt.launching.JRE_CONTAINER/org.eclipse.jdt.internal.debug.ui.launcher.StandardVMType/JavaSE-21/"/>
+	<classpathentry kind="con" path="org.eclipse.buildship.core.gradleclasspathcontainer"/>
+	<classpathentry kind="output" path="bin/default"/>
+</classpath>
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/.project b/node_modules/@ihealth/ihealthlibrary-react-native/android/.project
index df0d1c9..932b315 100644
--- a/node_modules/@ihealth/ihealthlibrary-react-native/android/.project
+++ b/node_modules/@ihealth/ihealthlibrary-react-native/android/.project
@@ -1,10 +1,15 @@
 <?xml version="1.0" encoding="UTF-8"?>
 <projectDescription>
-	<name>android</name>
+	<name>ihealth_ihealthlibrary-react-native</name>
 	<comment>Project android created by Buildship.</comment>
 	<projects>
 	</projects>
 	<buildSpec>
+		<buildCommand>
+			<name>org.eclipse.jdt.core.javabuilder</name>
+			<arguments>
+			</arguments>
+		</buildCommand>
 		<buildCommand>
 			<name>org.eclipse.buildship.core.gradleprojectbuilder</name>
 			<arguments>
@@ -12,16 +17,17 @@
 		</buildCommand>
 	</buildSpec>
 	<natures>
+		<nature>org.eclipse.jdt.core.javanature</nature>
 		<nature>org.eclipse.buildship.core.gradleprojectnature</nature>
 	</natures>
 	<filteredResources>
 		<filter>
-			<id>1623719603491</id>
+			<id>1752087210947</id>
 			<name></name>
 			<type>30</type>
 			<matcher>
 				<id>org.eclipse.core.resources.regexFilterMatcher</id>
-				<arguments>node_modules|.git|__CREATED_BY_JAVA_LANGUAGE_SERVER__</arguments>
+				<arguments>node_modules|\.git|__CREATED_BY_JAVA_LANGUAGE_SERVER__</arguments>
 			</matcher>
 		</filter>
 	</filteredResources>
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/.settings/org.eclipse.buildship.core.prefs b/node_modules/@ihealth/ihealthlibrary-react-native/android/.settings/org.eclipse.buildship.core.prefs
index dc1d8e8..1f575da 100644
--- a/node_modules/@ihealth/ihealthlibrary-react-native/android/.settings/org.eclipse.buildship.core.prefs
+++ b/node_modules/@ihealth/ihealthlibrary-react-native/android/.settings/org.eclipse.buildship.core.prefs
@@ -2,7 +2,7 @@ arguments=
 auto.sync=false
 build.scans.enabled=false
 connection.gradle.distribution=GRADLE_DISTRIBUTION(WRAPPER)
-connection.project.dir=
+connection.project.dir=../../../../android
 eclipse.preferences.version=1
 gradle.user.home=
 java.home=/Library/Java/JavaVirtualMachines/jdk-15.0.1.jdk/Contents/Home
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build.gradle b/node_modules/@ihealth/ihealthlibrary-react-native/android/build.gradle
index f21b8bc..7d68bf9 100644
--- a/node_modules/@ihealth/ihealthlibrary-react-native/android/build.gradle
+++ b/node_modules/@ihealth/ihealthlibrary-react-native/android/build.gradle
@@ -1,12 +1,12 @@
 apply plugin: 'com.android.library'
 
 android {
-    compileSdkVersion 28
-    buildToolsVersion '28.0.3'
+    compileSdkVersion 35
+    buildToolsVersion '33.0.0'
 
     defaultConfig {
-        minSdkVersion 18
-        targetSdkVersion 28
+        minSdkVersion 24
+        targetSdkVersion 35
         versionCode 1
         versionName "1.0"
 
@@ -41,3 +41,7 @@ dependencies {
     // implementation 'no.nordicsemi.android:dfu:1.6.1'
     implementation 'com.alibaba:fastjson:1.2.48'
 }
+
+// tasks.named('mergeDebugResources') {
+//     dependsOn ':ihealth_ihealthlibrary-react-native:generateDebugResValues'
+// }
\ No newline at end of file
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/.transforms/41560ceec5095289a58a20cfd3f66827/results.bin b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/.transforms/41560ceec5095289a58a20cfd3f66827/results.bin
new file mode 100644
index 0000000..0d259dd
--- /dev/null
+++ b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/.transforms/41560ceec5095289a58a20cfd3f66827/results.bin
@@ -0,0 +1 @@
+o/classes
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/.transforms/41560ceec5095289a58a20cfd3f66827/transformed/classes/classes_dex/classes.dex b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/.transforms/41560ceec5095289a58a20cfd3f66827/transformed/classes/classes_dex/classes.dex
new file mode 100644
index 0000000..f6802e7
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/.transforms/41560ceec5095289a58a20cfd3f66827/transformed/classes/classes_dex/classes.dex differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/.transforms/5a621f7fa5c18ccf3e6546e2e7bab68c/results.bin b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/.transforms/5a621f7fa5c18ccf3e6546e2e7bab68c/results.bin
new file mode 100644
index 0000000..0d259dd
--- /dev/null
+++ b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/.transforms/5a621f7fa5c18ccf3e6546e2e7bab68c/results.bin
@@ -0,0 +1 @@
+o/classes
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/.transforms/5a621f7fa5c18ccf3e6546e2e7bab68c/transformed/classes/classes_dex/classes.dex b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/.transforms/5a621f7fa5c18ccf3e6546e2e7bab68c/transformed/classes/classes_dex/classes.dex
new file mode 100644
index 0000000..3f3bebd
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/.transforms/5a621f7fa5c18ccf3e6546e2e7bab68c/transformed/classes/classes_dex/classes.dex differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/.transforms/9b23bd4b17bad4359282d1341be445dd/results.bin b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/.transforms/9b23bd4b17bad4359282d1341be445dd/results.bin
new file mode 100644
index 0000000..0d259dd
--- /dev/null
+++ b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/.transforms/9b23bd4b17bad4359282d1341be445dd/results.bin
@@ -0,0 +1 @@
+o/classes
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/.transforms/9b23bd4b17bad4359282d1341be445dd/transformed/classes/classes_dex/classes.dex b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/.transforms/9b23bd4b17bad4359282d1341be445dd/transformed/classes/classes_dex/classes.dex
new file mode 100644
index 0000000..f6802e7
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/.transforms/9b23bd4b17bad4359282d1341be445dd/transformed/classes/classes_dex/classes.dex differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/.transforms/f4f6f358a0c9e6322d12aaec95d149ab/results.bin b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/.transforms/f4f6f358a0c9e6322d12aaec95d149ab/results.bin
new file mode 100644
index 0000000..0d259dd
--- /dev/null
+++ b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/.transforms/f4f6f358a0c9e6322d12aaec95d149ab/results.bin
@@ -0,0 +1 @@
+o/classes
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/.transforms/f4f6f358a0c9e6322d12aaec95d149ab/transformed/classes/classes_dex/classes.dex b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/.transforms/f4f6f358a0c9e6322d12aaec95d149ab/transformed/classes/classes_dex/classes.dex
new file mode 100644
index 0000000..3f3bebd
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/.transforms/f4f6f358a0c9e6322d12aaec95d149ab/transformed/classes/classes_dex/classes.dex differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/generated/source/buildConfig/debug/com/ihealth/ihealthlibrary/BuildConfig.java b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/generated/source/buildConfig/debug/com/ihealth/ihealthlibrary/BuildConfig.java
new file mode 100644
index 0000000..38b5fbc
--- /dev/null
+++ b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/generated/source/buildConfig/debug/com/ihealth/ihealthlibrary/BuildConfig.java
@@ -0,0 +1,10 @@
+/**
+ * Automatically generated file. DO NOT MODIFY
+ */
+package com.ihealth.ihealthlibrary;
+
+public final class BuildConfig {
+  public static final boolean DEBUG = Boolean.parseBoolean("true");
+  public static final String LIBRARY_PACKAGE_NAME = "com.ihealth.ihealthlibrary";
+  public static final String BUILD_TYPE = "debug";
+}
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/aapt_friendly_merged_manifests/debug/processDebugManifest/aapt/AndroidManifest.xml b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/aapt_friendly_merged_manifests/debug/processDebugManifest/aapt/AndroidManifest.xml
new file mode 100644
index 0000000..79d5c42
--- /dev/null
+++ b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/aapt_friendly_merged_manifests/debug/processDebugManifest/aapt/AndroidManifest.xml
@@ -0,0 +1,12 @@
+<?xml version="1.0" encoding="utf-8"?>
+<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+    package="com.ihealth.ihealthlibrary" >
+
+    <uses-sdk android:minSdkVersion="24" />
+
+    <application
+        android:label="@string/app_name"
+        android:supportsRtl="true" >
+    </application>
+
+</manifest>
\ No newline at end of file
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/aapt_friendly_merged_manifests/debug/processDebugManifest/aapt/output-metadata.json b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/aapt_friendly_merged_manifests/debug/processDebugManifest/aapt/output-metadata.json
new file mode 100644
index 0000000..f895469
--- /dev/null
+++ b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/aapt_friendly_merged_manifests/debug/processDebugManifest/aapt/output-metadata.json
@@ -0,0 +1,18 @@
+{
+  "version": 3,
+  "artifactType": {
+    "type": "AAPT_FRIENDLY_MERGED_MANIFESTS",
+    "kind": "Directory"
+  },
+  "applicationId": "com.ihealth.ihealthlibrary",
+  "variantName": "debug",
+  "elements": [
+    {
+      "type": "SINGLE",
+      "filters": [],
+      "attributes": [],
+      "outputFile": "AndroidManifest.xml"
+    }
+  ],
+  "elementType": "File"
+}
\ No newline at end of file
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/aar_metadata/debug/writeDebugAarMetadata/aar-metadata.properties b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/aar_metadata/debug/writeDebugAarMetadata/aar-metadata.properties
new file mode 100644
index 0000000..1211b1e
--- /dev/null
+++ b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/aar_metadata/debug/writeDebugAarMetadata/aar-metadata.properties
@@ -0,0 +1,6 @@
+aarFormatVersion=1.0
+aarMetadataVersion=1.0
+minCompileSdk=1
+minCompileSdkExtension=0
+minAndroidGradlePluginVersion=1.0.0
+coreLibraryDesugaringEnabled=false
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/annotation_processor_list/debug/javaPreCompileDebug/annotationProcessors.json b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/annotation_processor_list/debug/javaPreCompileDebug/annotationProcessors.json
new file mode 100644
index 0000000..9e26dfe
--- /dev/null
+++ b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/annotation_processor_list/debug/javaPreCompileDebug/annotationProcessors.json
@@ -0,0 +1 @@
+{}
\ No newline at end of file
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/compile_library_classes_jar/debug/bundleLibCompileToJarDebug/classes.jar b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/compile_library_classes_jar/debug/bundleLibCompileToJarDebug/classes.jar
new file mode 100644
index 0000000..36bce9e
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/compile_library_classes_jar/debug/bundleLibCompileToJarDebug/classes.jar differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/compile_r_class_jar/debug/generateDebugRFile/R.jar b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/compile_r_class_jar/debug/generateDebugRFile/R.jar
new file mode 100644
index 0000000..c5227b7
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/compile_r_class_jar/debug/generateDebugRFile/R.jar differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/compile_symbol_list/debug/generateDebugRFile/R.txt b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/compile_symbol_list/debug/generateDebugRFile/R.txt
new file mode 100644
index 0000000..a9551e5
--- /dev/null
+++ b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/compile_symbol_list/debug/generateDebugRFile/R.txt
@@ -0,0 +1 @@
+int string app_name 0x0
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties
new file mode 100644
index 0000000..149b4ff
--- /dev/null
+++ b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties
@@ -0,0 +1 @@
+#Tue Jul 15 23:38:42 EDT 2025
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/incremental/debug/packageDebugResources/merged.dir/values/values.xml b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/incremental/debug/packageDebugResources/merged.dir/values/values.xml
new file mode 100644
index 0000000..580d89f
--- /dev/null
+++ b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/incremental/debug/packageDebugResources/merged.dir/values/values.xml
@@ -0,0 +1,4 @@
+<?xml version="1.0" encoding="utf-8"?>
+<resources>
+    <string name="app_name">iHealthLibrary</string>
+</resources>
\ No newline at end of file
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/incremental/debug/packageDebugResources/merger.xml b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/incremental/debug/packageDebugResources/merger.xml
new file mode 100644
index 0000000..4e50b49
--- /dev/null
+++ b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/incremental/debug/packageDebugResources/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\adaptit\node_modules\@ihealth\ihealthlibrary-react-native\android\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\adaptit\node_modules\@ihealth\ihealthlibrary-react-native\android\src\main\res"><file path="C:\adaptit\node_modules\@ihealth\ihealthlibrary-react-native\android\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">iHealthLibrary</string></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\adaptit\node_modules\@ihealth\ihealthlibrary-react-native\android\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\adaptit\node_modules\@ihealth\ihealthlibrary-react-native\android\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\adaptit\node_modules\@ihealth\ihealthlibrary-react-native\android\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\adaptit\node_modules\@ihealth\ihealthlibrary-react-native\android\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>
\ No newline at end of file
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/incremental/mergeDebugJniLibFolders/merger.xml b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/incremental/mergeDebugJniLibFolders/merger.xml
new file mode 100644
index 0000000..0f0ebfa
--- /dev/null
+++ b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/incremental/mergeDebugJniLibFolders/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\adaptit\node_modules\@ihealth\ihealthlibrary-react-native\android\libs"><file name="arm64-v8a/libBodyfat_SDK.so" path="C:\adaptit\node_modules\@ihealth\ihealthlibrary-react-native\android\libs\arm64-v8a\libBodyfat_SDK.so"/><file name="arm64-v8a/libECGOffline.so" path="C:\adaptit\node_modules\@ihealth\ihealthlibrary-react-native\android\libs\arm64-v8a\libECGOffline.so"/><file name="arm64-v8a/libECGOnline.so" path="C:\adaptit\node_modules\@ihealth\ihealthlibrary-react-native\android\libs\arm64-v8a\libECGOnline.so"/><file name="arm64-v8a/libiHealth.so" path="C:\adaptit\node_modules\@ihealth\ihealthlibrary-react-native\android\libs\arm64-v8a\libiHealth.so"/><file name="arm64-v8a/libVeryFitMulti.so" path="C:\adaptit\node_modules\@ihealth\ihealthlibrary-react-native\android\libs\arm64-v8a\libVeryFitMulti.so"/><file name="armeabi-v7a/libBodyfat_SDK.so" path="C:\adaptit\node_modules\@ihealth\ihealthlibrary-react-native\android\libs\armeabi-v7a\libBodyfat_SDK.so"/><file name="armeabi-v7a/libECGOffline.so" path="C:\adaptit\node_modules\@ihealth\ihealthlibrary-react-native\android\libs\armeabi-v7a\libECGOffline.so"/><file name="armeabi-v7a/libECGOnline.so" path="C:\adaptit\node_modules\@ihealth\ihealthlibrary-react-native\android\libs\armeabi-v7a\libECGOnline.so"/><file name="armeabi-v7a/libiHealth.so" path="C:\adaptit\node_modules\@ihealth\ihealthlibrary-react-native\android\libs\armeabi-v7a\libiHealth.so"/><file name="armeabi-v7a/libVeryFitMulti.so" path="C:\adaptit\node_modules\@ihealth\ihealthlibrary-react-native\android\libs\armeabi-v7a\libVeryFitMulti.so"/><file name="iHealthSDK_2.13.0.jar" path="C:\adaptit\node_modules\@ihealth\ihealthlibrary-react-native\android\libs\iHealthSDK_2.13.0.jar"/><file name="smartlinklib3.6.4_product.jar" path="C:\adaptit\node_modules\@ihealth\ihealthlibrary-react-native\android\libs\smartlinklib3.6.4_product.jar"/><file name="x86/libBodyfat_SDK.so" path="C:\adaptit\node_modules\@ihealth\ihealthlibrary-react-native\android\libs\x86\libBodyfat_SDK.so"/><file name="x86/libECGOffline.so" path="C:\adaptit\node_modules\@ihealth\ihealthlibrary-react-native\android\libs\x86\libECGOffline.so"/><file name="x86/libECGOnline.so" path="C:\adaptit\node_modules\@ihealth\ihealthlibrary-react-native\android\libs\x86\libECGOnline.so"/><file name="x86/libiHealth.so" path="C:\adaptit\node_modules\@ihealth\ihealthlibrary-react-native\android\libs\x86\libiHealth.so"/><file name="x86/libVeryFitMulti.so" path="C:\adaptit\node_modules\@ihealth\ihealthlibrary-react-native\android\libs\x86\libVeryFitMulti.so"/><file name="x86_64/libBodyfat_SDK.so" path="C:\adaptit\node_modules\@ihealth\ihealthlibrary-react-native\android\libs\x86_64\libBodyfat_SDK.so"/><file name="x86_64/libECGOffline.so" path="C:\adaptit\node_modules\@ihealth\ihealthlibrary-react-native\android\libs\x86_64\libECGOffline.so"/><file name="x86_64/libECGOnline.so" path="C:\adaptit\node_modules\@ihealth\ihealthlibrary-react-native\android\libs\x86_64\libECGOnline.so"/><file name="x86_64/libiHealth.so" path="C:\adaptit\node_modules\@ihealth\ihealthlibrary-react-native\android\libs\x86_64\libiHealth.so"/><file name="x86_64/libVeryFitMulti.so" path="C:\adaptit\node_modules\@ihealth\ihealthlibrary-react-native\android\libs\x86_64\libVeryFitMulti.so"/></source></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\adaptit\node_modules\@ihealth\ihealthlibrary-react-native\android\src\debug\jniLibs"/></dataSet></merger>
\ No newline at end of file
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/incremental/mergeDebugShaders/merger.xml b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/incremental/mergeDebugShaders/merger.xml
new file mode 100644
index 0000000..7c18fea
--- /dev/null
+++ b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/incremental/mergeDebugShaders/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\adaptit\node_modules\@ihealth\ihealthlibrary-react-native\android\src\main\shaders"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\adaptit\node_modules\@ihealth\ihealthlibrary-react-native\android\src\debug\shaders"/></dataSet></merger>
\ No newline at end of file
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/incremental/packageDebugAssets/merger.xml b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/incremental/packageDebugAssets/merger.xml
new file mode 100644
index 0000000..a55114d
--- /dev/null
+++ b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/incremental/packageDebugAssets/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\adaptit\node_modules\@ihealth\ihealthlibrary-react-native\android\src\main\assets"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\adaptit\node_modules\@ihealth\ihealthlibrary-react-native\android\src\debug\assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\adaptit\node_modules\@ihealth\ihealthlibrary-react-native\android\build\intermediates\shader_assets\debug\compileDebugShaders\out"/></dataSet></merger>
\ No newline at end of file
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/AM3SModule.class b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/AM3SModule.class
new file mode 100644
index 0000000..607be2b
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/AM3SModule.class differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/AM4Module.class b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/AM4Module.class
new file mode 100644
index 0000000..3a6dd51
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/AM4Module.class differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/AM5Module.class b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/AM5Module.class
new file mode 100644
index 0000000..46c548c
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/AM5Module.class differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/AM5ProfileModule.class b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/AM5ProfileModule.class
new file mode 100644
index 0000000..79ad314
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/AM5ProfileModule.class differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/AM6Module.class b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/AM6Module.class
new file mode 100644
index 0000000..6acc031
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/AM6Module.class differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/AM6ProfileModule.class b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/AM6ProfileModule.class
new file mode 100644
index 0000000..06cab79
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/AM6ProfileModule.class differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/AMProfileModule.class b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/AMProfileModule.class
new file mode 100644
index 0000000..4bf4fad
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/AMProfileModule.class differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/BG1AModule.class b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/BG1AModule.class
new file mode 100644
index 0000000..bad1b7a
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/BG1AModule.class differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/BG1AProfileModule.class b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/BG1AProfileModule.class
new file mode 100644
index 0000000..0e58d61
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/BG1AProfileModule.class differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/BG1Module.class b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/BG1Module.class
new file mode 100644
index 0000000..20ab630
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/BG1Module.class differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/BG1ProfileModule.class b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/BG1ProfileModule.class
new file mode 100644
index 0000000..023fa92
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/BG1ProfileModule.class differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/BG1SModule.class b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/BG1SModule.class
new file mode 100644
index 0000000..472802e
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/BG1SModule.class differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/BG1SProfileModule.class b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/BG1SProfileModule.class
new file mode 100644
index 0000000..497767a
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/BG1SProfileModule.class differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/BG5Module.class b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/BG5Module.class
new file mode 100644
index 0000000..79e8aa6
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/BG5Module.class differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/BG5SModule.class b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/BG5SModule.class
new file mode 100644
index 0000000..7c9d268
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/BG5SModule.class differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/BG5SProfileModule.class b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/BG5SProfileModule.class
new file mode 100644
index 0000000..0dac03b
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/BG5SProfileModule.class differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/BGProfileModule.class b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/BGProfileModule.class
new file mode 100644
index 0000000..c1185a1
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/BGProfileModule.class differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/BP3LModule.class b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/BP3LModule.class
new file mode 100644
index 0000000..0dcf047
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/BP3LModule.class differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/BP550BTModule.class b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/BP550BTModule.class
new file mode 100644
index 0000000..5343559
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/BP550BTModule.class differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/BP5Module.class b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/BP5Module.class
new file mode 100644
index 0000000..aaebab8
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/BP5Module.class differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/BP5SModule.class b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/BP5SModule.class
new file mode 100644
index 0000000..82eae45
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/BP5SModule.class differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/BP7Module.class b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/BP7Module.class
new file mode 100644
index 0000000..e08fe88
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/BP7Module.class differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/BP7SModule.class b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/BP7SModule.class
new file mode 100644
index 0000000..50ba2ef
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/BP7SModule.class differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/BPProfileModule.class b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/BPProfileModule.class
new file mode 100644
index 0000000..e29e95e
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/BPProfileModule.class differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/BTMModule.class b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/BTMModule.class
new file mode 100644
index 0000000..11bf51b
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/BTMModule.class differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/BTMProfileModule.class b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/BTMProfileModule.class
new file mode 100644
index 0000000..f8896f0
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/BTMProfileModule.class differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/BuildConfig.class b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/BuildConfig.class
new file mode 100644
index 0000000..f9f144a
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/BuildConfig.class differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/ECGModule.class b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/ECGModule.class
new file mode 100644
index 0000000..f92e2bc
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/ECGModule.class differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/ECGProfileModule.class b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/ECGProfileModule.class
new file mode 100644
index 0000000..d31434e
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/ECGProfileModule.class differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/ECGUSBModule.class b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/ECGUSBModule.class
new file mode 100644
index 0000000..55bc452
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/ECGUSBModule.class differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/HS2Module.class b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/HS2Module.class
new file mode 100644
index 0000000..52c73cf
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/HS2Module.class differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/HS2SModule.class b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/HS2SModule.class
new file mode 100644
index 0000000..ce20720
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/HS2SModule.class differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/HS2SProModule.class b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/HS2SProModule.class
new file mode 100644
index 0000000..12a4c2c
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/HS2SProModule.class differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/HS2SProfileModule.class b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/HS2SProfileModule.class
new file mode 100644
index 0000000..a88bd04
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/HS2SProfileModule.class differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/HS4SModule.class b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/HS4SModule.class
new file mode 100644
index 0000000..dd1cf1e
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/HS4SModule.class differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/HS6Module$1.class b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/HS6Module$1.class
new file mode 100644
index 0000000..56436f9
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/HS6Module$1.class differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/HS6Module.class b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/HS6Module.class
new file mode 100644
index 0000000..d81a2b6
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/HS6Module.class differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/HS6ProfileModule.class b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/HS6ProfileModule.class
new file mode 100644
index 0000000..f4204cf
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/HS6ProfileModule.class differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/HSProfileModule.class b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/HSProfileModule.class
new file mode 100644
index 0000000..60e5126
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/HSProfileModule.class differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/NT13BModule.class b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/NT13BModule.class
new file mode 100644
index 0000000..2da7d4d
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/NT13BModule.class differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/NT13BProfileModule.class b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/NT13BProfileModule.class
new file mode 100644
index 0000000..0948f1e
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/NT13BProfileModule.class differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/PO1Module.class b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/PO1Module.class
new file mode 100644
index 0000000..712e798
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/PO1Module.class differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/PO1ProfileModule.class b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/PO1ProfileModule.class
new file mode 100644
index 0000000..6a061aa
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/PO1ProfileModule.class differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/PO3Module.class b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/PO3Module.class
new file mode 100644
index 0000000..c9a3df8
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/PO3Module.class differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/POProfileModule.class b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/POProfileModule.class
new file mode 100644
index 0000000..a100d52
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/POProfileModule.class differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/PT3SBTModule.class b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/PT3SBTModule.class
new file mode 100644
index 0000000..20b764c
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/PT3SBTModule.class differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/PT3SBTProfileModule.class b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/PT3SBTProfileModule.class
new file mode 100644
index 0000000..7e9aa15
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/PT3SBTProfileModule.class differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/TS28BModule.class b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/TS28BModule.class
new file mode 100644
index 0000000..0201e27
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/TS28BModule.class differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/TS28BProfileModule.class b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/TS28BProfileModule.class
new file mode 100644
index 0000000..9e258f8
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/TS28BProfileModule.class differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/Utils.class b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/Utils.class
new file mode 100644
index 0000000..ac5a1c0
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/Utils.class differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/iHealthBaseModule.class b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/iHealthBaseModule.class
new file mode 100644
index 0000000..f576572
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/iHealthBaseModule.class differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/iHealthDeviceManagerModule$1.class b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/iHealthDeviceManagerModule$1.class
new file mode 100644
index 0000000..459d7f5
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/iHealthDeviceManagerModule$1.class differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/iHealthDeviceManagerModule$2.class b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/iHealthDeviceManagerModule$2.class
new file mode 100644
index 0000000..e5d3458
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/iHealthDeviceManagerModule$2.class differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/iHealthDeviceManagerModule.class b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/iHealthDeviceManagerModule.class
new file mode 100644
index 0000000..184fe18
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/iHealthDeviceManagerModule.class differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/iHealthDeviceManagerPackage.class b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/iHealthDeviceManagerPackage.class
new file mode 100644
index 0000000..98d7141
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/javac/debug/compileDebugJavaWithJavac/classes/com/ihealth/ihealthlibrary/iHealthDeviceManagerPackage.class differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/library_jni/debug/copyDebugJniLibsProjectOnly/jni/arm64-v8a/libBodyfat_SDK.so b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/library_jni/debug/copyDebugJniLibsProjectOnly/jni/arm64-v8a/libBodyfat_SDK.so
new file mode 100644
index 0000000..4fc680d
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/library_jni/debug/copyDebugJniLibsProjectOnly/jni/arm64-v8a/libBodyfat_SDK.so differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/library_jni/debug/copyDebugJniLibsProjectOnly/jni/arm64-v8a/libECGOffline.so b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/library_jni/debug/copyDebugJniLibsProjectOnly/jni/arm64-v8a/libECGOffline.so
new file mode 100644
index 0000000..60a9d84
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/library_jni/debug/copyDebugJniLibsProjectOnly/jni/arm64-v8a/libECGOffline.so differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/library_jni/debug/copyDebugJniLibsProjectOnly/jni/arm64-v8a/libECGOnline.so b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/library_jni/debug/copyDebugJniLibsProjectOnly/jni/arm64-v8a/libECGOnline.so
new file mode 100644
index 0000000..edffde2
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/library_jni/debug/copyDebugJniLibsProjectOnly/jni/arm64-v8a/libECGOnline.so differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/library_jni/debug/copyDebugJniLibsProjectOnly/jni/arm64-v8a/libVeryFitMulti.so b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/library_jni/debug/copyDebugJniLibsProjectOnly/jni/arm64-v8a/libVeryFitMulti.so
new file mode 100644
index 0000000..c58f6e8
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/library_jni/debug/copyDebugJniLibsProjectOnly/jni/arm64-v8a/libVeryFitMulti.so differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/library_jni/debug/copyDebugJniLibsProjectOnly/jni/arm64-v8a/libiHealth.so b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/library_jni/debug/copyDebugJniLibsProjectOnly/jni/arm64-v8a/libiHealth.so
new file mode 100644
index 0000000..5d7659c
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/library_jni/debug/copyDebugJniLibsProjectOnly/jni/arm64-v8a/libiHealth.so differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/library_jni/debug/copyDebugJniLibsProjectOnly/jni/armeabi-v7a/libBodyfat_SDK.so b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/library_jni/debug/copyDebugJniLibsProjectOnly/jni/armeabi-v7a/libBodyfat_SDK.so
new file mode 100644
index 0000000..4d3374f
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/library_jni/debug/copyDebugJniLibsProjectOnly/jni/armeabi-v7a/libBodyfat_SDK.so differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/library_jni/debug/copyDebugJniLibsProjectOnly/jni/armeabi-v7a/libECGOffline.so b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/library_jni/debug/copyDebugJniLibsProjectOnly/jni/armeabi-v7a/libECGOffline.so
new file mode 100644
index 0000000..3fa9085
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/library_jni/debug/copyDebugJniLibsProjectOnly/jni/armeabi-v7a/libECGOffline.so differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/library_jni/debug/copyDebugJniLibsProjectOnly/jni/armeabi-v7a/libECGOnline.so b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/library_jni/debug/copyDebugJniLibsProjectOnly/jni/armeabi-v7a/libECGOnline.so
new file mode 100644
index 0000000..792b7ac
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/library_jni/debug/copyDebugJniLibsProjectOnly/jni/armeabi-v7a/libECGOnline.so differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/library_jni/debug/copyDebugJniLibsProjectOnly/jni/armeabi-v7a/libVeryFitMulti.so b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/library_jni/debug/copyDebugJniLibsProjectOnly/jni/armeabi-v7a/libVeryFitMulti.so
new file mode 100644
index 0000000..e254b6f
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/library_jni/debug/copyDebugJniLibsProjectOnly/jni/armeabi-v7a/libVeryFitMulti.so differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/library_jni/debug/copyDebugJniLibsProjectOnly/jni/armeabi-v7a/libiHealth.so b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/library_jni/debug/copyDebugJniLibsProjectOnly/jni/armeabi-v7a/libiHealth.so
new file mode 100644
index 0000000..5def22d
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/library_jni/debug/copyDebugJniLibsProjectOnly/jni/armeabi-v7a/libiHealth.so differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/library_jni/debug/copyDebugJniLibsProjectOnly/jni/x86/libBodyfat_SDK.so b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/library_jni/debug/copyDebugJniLibsProjectOnly/jni/x86/libBodyfat_SDK.so
new file mode 100644
index 0000000..2cff220
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/library_jni/debug/copyDebugJniLibsProjectOnly/jni/x86/libBodyfat_SDK.so differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/library_jni/debug/copyDebugJniLibsProjectOnly/jni/x86/libECGOffline.so b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/library_jni/debug/copyDebugJniLibsProjectOnly/jni/x86/libECGOffline.so
new file mode 100644
index 0000000..96001b5
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/library_jni/debug/copyDebugJniLibsProjectOnly/jni/x86/libECGOffline.so differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/library_jni/debug/copyDebugJniLibsProjectOnly/jni/x86/libECGOnline.so b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/library_jni/debug/copyDebugJniLibsProjectOnly/jni/x86/libECGOnline.so
new file mode 100644
index 0000000..23621f3
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/library_jni/debug/copyDebugJniLibsProjectOnly/jni/x86/libECGOnline.so differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/library_jni/debug/copyDebugJniLibsProjectOnly/jni/x86/libVeryFitMulti.so b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/library_jni/debug/copyDebugJniLibsProjectOnly/jni/x86/libVeryFitMulti.so
new file mode 100644
index 0000000..79c513f
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/library_jni/debug/copyDebugJniLibsProjectOnly/jni/x86/libVeryFitMulti.so differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/library_jni/debug/copyDebugJniLibsProjectOnly/jni/x86/libiHealth.so b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/library_jni/debug/copyDebugJniLibsProjectOnly/jni/x86/libiHealth.so
new file mode 100644
index 0000000..355d1ae
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/library_jni/debug/copyDebugJniLibsProjectOnly/jni/x86/libiHealth.so differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/library_jni/debug/copyDebugJniLibsProjectOnly/jni/x86_64/libBodyfat_SDK.so b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/library_jni/debug/copyDebugJniLibsProjectOnly/jni/x86_64/libBodyfat_SDK.so
new file mode 100644
index 0000000..c76e666
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/library_jni/debug/copyDebugJniLibsProjectOnly/jni/x86_64/libBodyfat_SDK.so differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/library_jni/debug/copyDebugJniLibsProjectOnly/jni/x86_64/libECGOffline.so b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/library_jni/debug/copyDebugJniLibsProjectOnly/jni/x86_64/libECGOffline.so
new file mode 100644
index 0000000..c2bb960
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/library_jni/debug/copyDebugJniLibsProjectOnly/jni/x86_64/libECGOffline.so differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/library_jni/debug/copyDebugJniLibsProjectOnly/jni/x86_64/libECGOnline.so b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/library_jni/debug/copyDebugJniLibsProjectOnly/jni/x86_64/libECGOnline.so
new file mode 100644
index 0000000..8871982
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/library_jni/debug/copyDebugJniLibsProjectOnly/jni/x86_64/libECGOnline.so differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/library_jni/debug/copyDebugJniLibsProjectOnly/jni/x86_64/libVeryFitMulti.so b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/library_jni/debug/copyDebugJniLibsProjectOnly/jni/x86_64/libVeryFitMulti.so
new file mode 100644
index 0000000..4af84a5
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/library_jni/debug/copyDebugJniLibsProjectOnly/jni/x86_64/libVeryFitMulti.so differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/library_jni/debug/copyDebugJniLibsProjectOnly/jni/x86_64/libiHealth.so b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/library_jni/debug/copyDebugJniLibsProjectOnly/jni/x86_64/libiHealth.so
new file mode 100644
index 0000000..a4c6f9d
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/library_jni/debug/copyDebugJniLibsProjectOnly/jni/x86_64/libiHealth.so differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/local_only_symbol_list/debug/parseDebugLocalResources/R-def.txt b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/local_only_symbol_list/debug/parseDebugLocalResources/R-def.txt
new file mode 100644
index 0000000..34ca0ec
--- /dev/null
+++ b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/local_only_symbol_list/debug/parseDebugLocalResources/R-def.txt
@@ -0,0 +1,3 @@
+R_DEF: Internal format may change without notice
+local
+string app_name
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/manifest_merge_blame_file/debug/processDebugManifest/manifest-merger-blame-debug-report.txt b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/manifest_merge_blame_file/debug/processDebugManifest/manifest-merger-blame-debug-report.txt
new file mode 100644
index 0000000..d87f4f6
--- /dev/null
+++ b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/manifest_merge_blame_file/debug/processDebugManifest/manifest-merger-blame-debug-report.txt
@@ -0,0 +1,15 @@
+1<?xml version="1.0" encoding="utf-8"?>
+2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+3    package="com.ihealth.ihealthlibrary" >
+4
+5    <uses-sdk android:minSdkVersion="24" />
+6
+7    <application
+7-->C:\adaptit\node_modules\@ihealth\ihealthlibrary-react-native\android\src\main\AndroidManifest.xml:3:5-6:19
+8        android:label="@string/app_name"
+8-->C:\adaptit\node_modules\@ihealth\ihealthlibrary-react-native\android\src\main\AndroidManifest.xml:4:9-41
+9        android:supportsRtl="true" >
+9-->C:\adaptit\node_modules\@ihealth\ihealthlibrary-react-native\android\src\main\AndroidManifest.xml:5:9-35
+10    </application>
+11
+12</manifest>
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_jni_libs/debug/mergeDebugJniLibFolders/out/arm64-v8a/libBodyfat_SDK.so b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_jni_libs/debug/mergeDebugJniLibFolders/out/arm64-v8a/libBodyfat_SDK.so
new file mode 100644
index 0000000..4fc680d
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_jni_libs/debug/mergeDebugJniLibFolders/out/arm64-v8a/libBodyfat_SDK.so differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_jni_libs/debug/mergeDebugJniLibFolders/out/arm64-v8a/libECGOffline.so b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_jni_libs/debug/mergeDebugJniLibFolders/out/arm64-v8a/libECGOffline.so
new file mode 100644
index 0000000..60a9d84
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_jni_libs/debug/mergeDebugJniLibFolders/out/arm64-v8a/libECGOffline.so differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_jni_libs/debug/mergeDebugJniLibFolders/out/arm64-v8a/libECGOnline.so b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_jni_libs/debug/mergeDebugJniLibFolders/out/arm64-v8a/libECGOnline.so
new file mode 100644
index 0000000..edffde2
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_jni_libs/debug/mergeDebugJniLibFolders/out/arm64-v8a/libECGOnline.so differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_jni_libs/debug/mergeDebugJniLibFolders/out/arm64-v8a/libVeryFitMulti.so b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_jni_libs/debug/mergeDebugJniLibFolders/out/arm64-v8a/libVeryFitMulti.so
new file mode 100644
index 0000000..c58f6e8
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_jni_libs/debug/mergeDebugJniLibFolders/out/arm64-v8a/libVeryFitMulti.so differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_jni_libs/debug/mergeDebugJniLibFolders/out/arm64-v8a/libiHealth.so b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_jni_libs/debug/mergeDebugJniLibFolders/out/arm64-v8a/libiHealth.so
new file mode 100644
index 0000000..5d7659c
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_jni_libs/debug/mergeDebugJniLibFolders/out/arm64-v8a/libiHealth.so differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_jni_libs/debug/mergeDebugJniLibFolders/out/armeabi-v7a/libBodyfat_SDK.so b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_jni_libs/debug/mergeDebugJniLibFolders/out/armeabi-v7a/libBodyfat_SDK.so
new file mode 100644
index 0000000..4d3374f
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_jni_libs/debug/mergeDebugJniLibFolders/out/armeabi-v7a/libBodyfat_SDK.so differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_jni_libs/debug/mergeDebugJniLibFolders/out/armeabi-v7a/libECGOffline.so b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_jni_libs/debug/mergeDebugJniLibFolders/out/armeabi-v7a/libECGOffline.so
new file mode 100644
index 0000000..3fa9085
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_jni_libs/debug/mergeDebugJniLibFolders/out/armeabi-v7a/libECGOffline.so differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_jni_libs/debug/mergeDebugJniLibFolders/out/armeabi-v7a/libECGOnline.so b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_jni_libs/debug/mergeDebugJniLibFolders/out/armeabi-v7a/libECGOnline.so
new file mode 100644
index 0000000..792b7ac
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_jni_libs/debug/mergeDebugJniLibFolders/out/armeabi-v7a/libECGOnline.so differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_jni_libs/debug/mergeDebugJniLibFolders/out/armeabi-v7a/libVeryFitMulti.so b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_jni_libs/debug/mergeDebugJniLibFolders/out/armeabi-v7a/libVeryFitMulti.so
new file mode 100644
index 0000000..e254b6f
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_jni_libs/debug/mergeDebugJniLibFolders/out/armeabi-v7a/libVeryFitMulti.so differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_jni_libs/debug/mergeDebugJniLibFolders/out/armeabi-v7a/libiHealth.so b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_jni_libs/debug/mergeDebugJniLibFolders/out/armeabi-v7a/libiHealth.so
new file mode 100644
index 0000000..5def22d
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_jni_libs/debug/mergeDebugJniLibFolders/out/armeabi-v7a/libiHealth.so differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_jni_libs/debug/mergeDebugJniLibFolders/out/iHealthSDK_2.13.0.jar b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_jni_libs/debug/mergeDebugJniLibFolders/out/iHealthSDK_2.13.0.jar
new file mode 100644
index 0000000..01276f2
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_jni_libs/debug/mergeDebugJniLibFolders/out/iHealthSDK_2.13.0.jar differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_jni_libs/debug/mergeDebugJniLibFolders/out/smartlinklib3.6.4_product.jar b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_jni_libs/debug/mergeDebugJniLibFolders/out/smartlinklib3.6.4_product.jar
new file mode 100644
index 0000000..7152e5f
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_jni_libs/debug/mergeDebugJniLibFolders/out/smartlinklib3.6.4_product.jar differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_jni_libs/debug/mergeDebugJniLibFolders/out/x86/libBodyfat_SDK.so b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_jni_libs/debug/mergeDebugJniLibFolders/out/x86/libBodyfat_SDK.so
new file mode 100644
index 0000000..2cff220
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_jni_libs/debug/mergeDebugJniLibFolders/out/x86/libBodyfat_SDK.so differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_jni_libs/debug/mergeDebugJniLibFolders/out/x86/libECGOffline.so b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_jni_libs/debug/mergeDebugJniLibFolders/out/x86/libECGOffline.so
new file mode 100644
index 0000000..96001b5
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_jni_libs/debug/mergeDebugJniLibFolders/out/x86/libECGOffline.so differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_jni_libs/debug/mergeDebugJniLibFolders/out/x86/libECGOnline.so b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_jni_libs/debug/mergeDebugJniLibFolders/out/x86/libECGOnline.so
new file mode 100644
index 0000000..23621f3
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_jni_libs/debug/mergeDebugJniLibFolders/out/x86/libECGOnline.so differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_jni_libs/debug/mergeDebugJniLibFolders/out/x86/libVeryFitMulti.so b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_jni_libs/debug/mergeDebugJniLibFolders/out/x86/libVeryFitMulti.so
new file mode 100644
index 0000000..79c513f
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_jni_libs/debug/mergeDebugJniLibFolders/out/x86/libVeryFitMulti.so differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_jni_libs/debug/mergeDebugJniLibFolders/out/x86/libiHealth.so b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_jni_libs/debug/mergeDebugJniLibFolders/out/x86/libiHealth.so
new file mode 100644
index 0000000..355d1ae
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_jni_libs/debug/mergeDebugJniLibFolders/out/x86/libiHealth.so differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_jni_libs/debug/mergeDebugJniLibFolders/out/x86_64/libBodyfat_SDK.so b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_jni_libs/debug/mergeDebugJniLibFolders/out/x86_64/libBodyfat_SDK.so
new file mode 100644
index 0000000..c76e666
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_jni_libs/debug/mergeDebugJniLibFolders/out/x86_64/libBodyfat_SDK.so differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_jni_libs/debug/mergeDebugJniLibFolders/out/x86_64/libECGOffline.so b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_jni_libs/debug/mergeDebugJniLibFolders/out/x86_64/libECGOffline.so
new file mode 100644
index 0000000..c2bb960
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_jni_libs/debug/mergeDebugJniLibFolders/out/x86_64/libECGOffline.so differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_jni_libs/debug/mergeDebugJniLibFolders/out/x86_64/libECGOnline.so b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_jni_libs/debug/mergeDebugJniLibFolders/out/x86_64/libECGOnline.so
new file mode 100644
index 0000000..8871982
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_jni_libs/debug/mergeDebugJniLibFolders/out/x86_64/libECGOnline.so differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_jni_libs/debug/mergeDebugJniLibFolders/out/x86_64/libVeryFitMulti.so b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_jni_libs/debug/mergeDebugJniLibFolders/out/x86_64/libVeryFitMulti.so
new file mode 100644
index 0000000..4af84a5
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_jni_libs/debug/mergeDebugJniLibFolders/out/x86_64/libVeryFitMulti.so differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_jni_libs/debug/mergeDebugJniLibFolders/out/x86_64/libiHealth.so b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_jni_libs/debug/mergeDebugJniLibFolders/out/x86_64/libiHealth.so
new file mode 100644
index 0000000..a4c6f9d
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_jni_libs/debug/mergeDebugJniLibFolders/out/x86_64/libiHealth.so differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml
new file mode 100644
index 0000000..79d5c42
--- /dev/null
+++ b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml
@@ -0,0 +1,12 @@
+<?xml version="1.0" encoding="utf-8"?>
+<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+    package="com.ihealth.ihealthlibrary" >
+
+    <uses-sdk android:minSdkVersion="24" />
+
+    <application
+        android:label="@string/app_name"
+        android:supportsRtl="true" >
+    </application>
+
+</manifest>
\ No newline at end of file
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_native_libs/debug/mergeDebugNativeLibs/out/lib/arm64-v8a/libBodyfat_SDK.so b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_native_libs/debug/mergeDebugNativeLibs/out/lib/arm64-v8a/libBodyfat_SDK.so
new file mode 100644
index 0000000..4fc680d
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_native_libs/debug/mergeDebugNativeLibs/out/lib/arm64-v8a/libBodyfat_SDK.so differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_native_libs/debug/mergeDebugNativeLibs/out/lib/arm64-v8a/libECGOffline.so b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_native_libs/debug/mergeDebugNativeLibs/out/lib/arm64-v8a/libECGOffline.so
new file mode 100644
index 0000000..60a9d84
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_native_libs/debug/mergeDebugNativeLibs/out/lib/arm64-v8a/libECGOffline.so differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_native_libs/debug/mergeDebugNativeLibs/out/lib/arm64-v8a/libECGOnline.so b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_native_libs/debug/mergeDebugNativeLibs/out/lib/arm64-v8a/libECGOnline.so
new file mode 100644
index 0000000..edffde2
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_native_libs/debug/mergeDebugNativeLibs/out/lib/arm64-v8a/libECGOnline.so differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_native_libs/debug/mergeDebugNativeLibs/out/lib/arm64-v8a/libVeryFitMulti.so b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_native_libs/debug/mergeDebugNativeLibs/out/lib/arm64-v8a/libVeryFitMulti.so
new file mode 100644
index 0000000..c58f6e8
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_native_libs/debug/mergeDebugNativeLibs/out/lib/arm64-v8a/libVeryFitMulti.so differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_native_libs/debug/mergeDebugNativeLibs/out/lib/arm64-v8a/libiHealth.so b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_native_libs/debug/mergeDebugNativeLibs/out/lib/arm64-v8a/libiHealth.so
new file mode 100644
index 0000000..5d7659c
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_native_libs/debug/mergeDebugNativeLibs/out/lib/arm64-v8a/libiHealth.so differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_native_libs/debug/mergeDebugNativeLibs/out/lib/armeabi-v7a/libBodyfat_SDK.so b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_native_libs/debug/mergeDebugNativeLibs/out/lib/armeabi-v7a/libBodyfat_SDK.so
new file mode 100644
index 0000000..4d3374f
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_native_libs/debug/mergeDebugNativeLibs/out/lib/armeabi-v7a/libBodyfat_SDK.so differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_native_libs/debug/mergeDebugNativeLibs/out/lib/armeabi-v7a/libECGOffline.so b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_native_libs/debug/mergeDebugNativeLibs/out/lib/armeabi-v7a/libECGOffline.so
new file mode 100644
index 0000000..3fa9085
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_native_libs/debug/mergeDebugNativeLibs/out/lib/armeabi-v7a/libECGOffline.so differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_native_libs/debug/mergeDebugNativeLibs/out/lib/armeabi-v7a/libECGOnline.so b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_native_libs/debug/mergeDebugNativeLibs/out/lib/armeabi-v7a/libECGOnline.so
new file mode 100644
index 0000000..792b7ac
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_native_libs/debug/mergeDebugNativeLibs/out/lib/armeabi-v7a/libECGOnline.so differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_native_libs/debug/mergeDebugNativeLibs/out/lib/armeabi-v7a/libVeryFitMulti.so b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_native_libs/debug/mergeDebugNativeLibs/out/lib/armeabi-v7a/libVeryFitMulti.so
new file mode 100644
index 0000000..e254b6f
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_native_libs/debug/mergeDebugNativeLibs/out/lib/armeabi-v7a/libVeryFitMulti.so differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_native_libs/debug/mergeDebugNativeLibs/out/lib/armeabi-v7a/libiHealth.so b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_native_libs/debug/mergeDebugNativeLibs/out/lib/armeabi-v7a/libiHealth.so
new file mode 100644
index 0000000..5def22d
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_native_libs/debug/mergeDebugNativeLibs/out/lib/armeabi-v7a/libiHealth.so differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_native_libs/debug/mergeDebugNativeLibs/out/lib/x86/libBodyfat_SDK.so b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_native_libs/debug/mergeDebugNativeLibs/out/lib/x86/libBodyfat_SDK.so
new file mode 100644
index 0000000..2cff220
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_native_libs/debug/mergeDebugNativeLibs/out/lib/x86/libBodyfat_SDK.so differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_native_libs/debug/mergeDebugNativeLibs/out/lib/x86/libECGOffline.so b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_native_libs/debug/mergeDebugNativeLibs/out/lib/x86/libECGOffline.so
new file mode 100644
index 0000000..96001b5
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_native_libs/debug/mergeDebugNativeLibs/out/lib/x86/libECGOffline.so differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_native_libs/debug/mergeDebugNativeLibs/out/lib/x86/libECGOnline.so b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_native_libs/debug/mergeDebugNativeLibs/out/lib/x86/libECGOnline.so
new file mode 100644
index 0000000..23621f3
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_native_libs/debug/mergeDebugNativeLibs/out/lib/x86/libECGOnline.so differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_native_libs/debug/mergeDebugNativeLibs/out/lib/x86/libVeryFitMulti.so b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_native_libs/debug/mergeDebugNativeLibs/out/lib/x86/libVeryFitMulti.so
new file mode 100644
index 0000000..79c513f
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_native_libs/debug/mergeDebugNativeLibs/out/lib/x86/libVeryFitMulti.so differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_native_libs/debug/mergeDebugNativeLibs/out/lib/x86/libiHealth.so b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_native_libs/debug/mergeDebugNativeLibs/out/lib/x86/libiHealth.so
new file mode 100644
index 0000000..355d1ae
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_native_libs/debug/mergeDebugNativeLibs/out/lib/x86/libiHealth.so differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_native_libs/debug/mergeDebugNativeLibs/out/lib/x86_64/libBodyfat_SDK.so b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_native_libs/debug/mergeDebugNativeLibs/out/lib/x86_64/libBodyfat_SDK.so
new file mode 100644
index 0000000..c76e666
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_native_libs/debug/mergeDebugNativeLibs/out/lib/x86_64/libBodyfat_SDK.so differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_native_libs/debug/mergeDebugNativeLibs/out/lib/x86_64/libECGOffline.so b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_native_libs/debug/mergeDebugNativeLibs/out/lib/x86_64/libECGOffline.so
new file mode 100644
index 0000000..c2bb960
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_native_libs/debug/mergeDebugNativeLibs/out/lib/x86_64/libECGOffline.so differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_native_libs/debug/mergeDebugNativeLibs/out/lib/x86_64/libECGOnline.so b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_native_libs/debug/mergeDebugNativeLibs/out/lib/x86_64/libECGOnline.so
new file mode 100644
index 0000000..8871982
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_native_libs/debug/mergeDebugNativeLibs/out/lib/x86_64/libECGOnline.so differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_native_libs/debug/mergeDebugNativeLibs/out/lib/x86_64/libVeryFitMulti.so b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_native_libs/debug/mergeDebugNativeLibs/out/lib/x86_64/libVeryFitMulti.so
new file mode 100644
index 0000000..4af84a5
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_native_libs/debug/mergeDebugNativeLibs/out/lib/x86_64/libVeryFitMulti.so differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_native_libs/debug/mergeDebugNativeLibs/out/lib/x86_64/libiHealth.so b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_native_libs/debug/mergeDebugNativeLibs/out/lib/x86_64/libiHealth.so
new file mode 100644
index 0000000..a4c6f9d
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/merged_native_libs/debug/mergeDebugNativeLibs/out/lib/x86_64/libiHealth.so differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/navigation_json/debug/extractDeepLinksDebug/navigation.json b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/navigation_json/debug/extractDeepLinksDebug/navigation.json
new file mode 100644
index 0000000..0637a08
--- /dev/null
+++ b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/navigation_json/debug/extractDeepLinksDebug/navigation.json
@@ -0,0 +1 @@
+[]
\ No newline at end of file
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/nested_resources_validation_report/debug/generateDebugResources/nestedResourcesValidationReport.txt b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/nested_resources_validation_report/debug/generateDebugResources/nestedResourcesValidationReport.txt
new file mode 100644
index 0000000..08f4ebe
--- /dev/null
+++ b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/nested_resources_validation_report/debug/generateDebugResources/nestedResourcesValidationReport.txt
@@ -0,0 +1 @@
+0 Warning/Error
\ No newline at end of file
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/packaged_res/debug/packageDebugResources/values/values.xml b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/packaged_res/debug/packageDebugResources/values/values.xml
new file mode 100644
index 0000000..580d89f
--- /dev/null
+++ b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/packaged_res/debug/packageDebugResources/values/values.xml
@@ -0,0 +1,4 @@
+<?xml version="1.0" encoding="utf-8"?>
+<resources>
+    <string name="app_name">iHealthLibrary</string>
+</resources>
\ No newline at end of file
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/runtime_library_classes_jar/debug/bundleLibRuntimeToJarDebug/classes.jar b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/runtime_library_classes_jar/debug/bundleLibRuntimeToJarDebug/classes.jar
new file mode 100644
index 0000000..f2274cb
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/runtime_library_classes_jar/debug/bundleLibRuntimeToJarDebug/classes.jar differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/symbol_list_with_package_name/debug/generateDebugRFile/package-aware-r.txt b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/symbol_list_with_package_name/debug/generateDebugRFile/package-aware-r.txt
new file mode 100644
index 0000000..96d29be
--- /dev/null
+++ b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/intermediates/symbol_list_with_package_name/debug/generateDebugRFile/package-aware-r.txt
@@ -0,0 +1,2 @@
+com.ihealth.ihealthlibrary
+string app_name
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/outputs/logs/manifest-merger-debug-report.txt b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/outputs/logs/manifest-merger-debug-report.txt
new file mode 100644
index 0000000..c6ac2e8
--- /dev/null
+++ b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/outputs/logs/manifest-merger-debug-report.txt
@@ -0,0 +1,23 @@
+-- Merging decision tree log ---
+manifest
+ADDED from C:\adaptit\node_modules\@ihealth\ihealthlibrary-react-native\android\src\main\AndroidManifest.xml:1:1-7:12
+INJECTED from C:\adaptit\node_modules\@ihealth\ihealthlibrary-react-native\android\src\main\AndroidManifest.xml:1:1-7:12
+	package
+		ADDED from C:\adaptit\node_modules\@ihealth\ihealthlibrary-react-native\android\src\main\AndroidManifest.xml:2:5-41
+		INJECTED from C:\adaptit\node_modules\@ihealth\ihealthlibrary-react-native\android\src\main\AndroidManifest.xml
+	xmlns:android
+		ADDED from C:\adaptit\node_modules\@ihealth\ihealthlibrary-react-native\android\src\main\AndroidManifest.xml:1:11-69
+application
+ADDED from C:\adaptit\node_modules\@ihealth\ihealthlibrary-react-native\android\src\main\AndroidManifest.xml:3:5-6:19
+	android:supportsRtl
+		ADDED from C:\adaptit\node_modules\@ihealth\ihealthlibrary-react-native\android\src\main\AndroidManifest.xml:5:9-35
+	android:label
+		ADDED from C:\adaptit\node_modules\@ihealth\ihealthlibrary-react-native\android\src\main\AndroidManifest.xml:4:9-41
+uses-sdk
+INJECTED from C:\adaptit\node_modules\@ihealth\ihealthlibrary-react-native\android\src\main\AndroidManifest.xml reason: use-sdk injection requested
+INJECTED from C:\adaptit\node_modules\@ihealth\ihealthlibrary-react-native\android\src\main\AndroidManifest.xml
+INJECTED from C:\adaptit\node_modules\@ihealth\ihealthlibrary-react-native\android\src\main\AndroidManifest.xml
+	android:targetSdkVersion
+		INJECTED from C:\adaptit\node_modules\@ihealth\ihealthlibrary-react-native\android\src\main\AndroidManifest.xml
+	android:minSdkVersion
+		INJECTED from C:\adaptit\node_modules\@ihealth\ihealthlibrary-react-native\android\src\main\AndroidManifest.xml
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/build/tmp/compileDebugJavaWithJavac/previous-compilation-data.bin b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/tmp/compileDebugJavaWithJavac/previous-compilation-data.bin
new file mode 100644
index 0000000..b52f120
Binary files /dev/null and b/node_modules/@ihealth/ihealthlibrary-react-native/android/build/tmp/compileDebugJavaWithJavac/previous-compilation-data.bin differ
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/android/src/main/java/com/ihealth/ihealthlibrary/iHealthDeviceManagerModule.java b/node_modules/@ihealth/ihealthlibrary-react-native/android/src/main/java/com/ihealth/ihealthlibrary/iHealthDeviceManagerModule.java
index b9edb9c..2718e9b 100644
--- a/node_modules/@ihealth/ihealthlibrary-react-native/android/src/main/java/com/ihealth/ihealthlibrary/iHealthDeviceManagerModule.java
+++ b/node_modules/@ihealth/ihealthlibrary-react-native/android/src/main/java/com/ihealth/ihealthlibrary/iHealthDeviceManagerModule.java
@@ -41,29 +41,29 @@ public class iHealthDeviceManagerModule extends iHealthBaseModule implements Lif
     private static final String modelName = "iHealthDeviceManagerModule";
     private static final String TAG = "iHealthModel";
 
-    private final static String AM3S  = "AM3S";
-    private final static String AM4   = "AM4";
-    private final static String AM5   = "AM5";
-    private final static String AM6   = "AM6";
-    private final static String PO3   = "PO3";
-    private final static String BP5   = "BP5";
-    private final static String BP5S  = "BP5S";
-    private final static String BP3L  = "BP3L";
-    private final static String BP7   = "BP7";
-    private final static String BP7S  = "BP7S";
+    private final static String AM3S = "AM3S";
+    private final static String AM4 = "AM4";
+    private final static String AM5 = "AM5";
+    private final static String AM6 = "AM6";
+    private final static String PO3 = "PO3";
+    private final static String BP5 = "BP5";
+    private final static String BP5S = "BP5S";
+    private final static String BP3L = "BP3L";
+    private final static String BP7 = "BP7";
+    private final static String BP7S = "BP7S";
     private final static String KN550 = "KN550";
-    private final static String HS2   = "HS2";
-    private final static String HS2S  = "HS2S";
-    private final static String HS2SPRO  = "HS2S Pro";
-    private final static String HS4   = "HS4";
-    private final static String HS4S  = "HS4S";
-    //    private final static String HS6 = "HS6";
-    private final static String BG1  = "BG1";
+    private final static String HS2 = "HS2";
+    private final static String HS2S = "HS2S";
+    private final static String HS2SPRO = "HS2S Pro";
+    private final static String HS4 = "HS4";
+    private final static String HS4S = "HS4S";
+    // private final static String HS6 = "HS6";
+    private final static String BG1 = "BG1";
     private final static String BG1S = "BG1S";
-    private final static String BG5  = "BG5";
+    private final static String BG5 = "BG5";
     private final static String BG5S = "BG5S";
     private final static String BG5L = "BG5L";
-    private final static String BTM  = "BTM";
+    private final static String BTM = "BTM";
     private final static String ECG3 = "ECG3";
     private final static String ECG3USB = "ECG3USB";
 
@@ -73,7 +73,6 @@ public class iHealthDeviceManagerModule extends iHealthBaseModule implements Lif
     private final static String PO1 = "PO1";
     private final static String BG1A = "BG1A";
 
-
     private final static String Event_Scan_Device = "event_scan_device";
     private final static String Event_Scan_Finish = "event_scan_finish";
     private final static String Event_Device_Connected = "event_device_connected";
@@ -84,7 +83,7 @@ public class iHealthDeviceManagerModule extends iHealthBaseModule implements Lif
     private static int callbackId;
     private ReactApplicationContext mContext;
     private String userName;
-    private String mac = "";//macAddressForBg1
+    private String mac = "";// macAddressForBg1
     private static final String DESCRIPTION = "description";
     private static final String IDPS = "idps";
     private String idps = "";
@@ -94,8 +93,10 @@ public class iHealthDeviceManagerModule extends iHealthBaseModule implements Lif
         mContext = reactContext;
         reactContext.addLifecycleEventListener(this);
 
-       /* iHealthDevicesManager.getInstance().init(mContext, Log.VERBOSE, Log.WARN);
-        Bg1Control.getInstance().init(mContext, "", 0, true);*/
+        /*
+         * iHealthDevicesManager.getInstance().init(mContext, Log.VERBOSE, Log.WARN);
+         * Bg1Control.getInstance().init(mContext, "", 0, true);
+         */
 
     }
 
@@ -103,23 +104,24 @@ public class iHealthDeviceManagerModule extends iHealthBaseModule implements Lif
 
         @Override
         public void onScanDevice(String mac, String deviceType, int rssi, Map manufactorData) {
-          WritableMap params = Arguments.createMap();
-          params.putString("mac", mac);
-          if (deviceType.equals("ECGUSB")) {
-              params.putString("type", "ECG3USB");
-
-          } else if (deviceType.equals("KN-550BT")) {
-              params.putString("type", "KN550");
-
-          } else {
-              params.putString("type", deviceType);
-          }
-          params.putInt("rssi", rssi);
-          sendEvent(Event_Scan_Device, params);
+            WritableMap params = Arguments.createMap();
+            params.putString("mac", mac);
+            if (deviceType.equals("ECGUSB")) {
+                params.putString("type", "ECG3USB");
+
+            } else if (deviceType.equals("KN-550BT")) {
+                params.putString("type", "KN550");
+
+            } else {
+                params.putString("type", deviceType);
+            }
+            params.putInt("rssi", rssi);
+            sendEvent(Event_Scan_Device, params);
         }
 
         @Override
-        public void onDeviceConnectionStateChange(String mac, String deviceType, int status, int errorID, Map manufactorData) {
+        public void onDeviceConnectionStateChange(String mac, String deviceType, int status, int errorID,
+                Map manufactorData) {
             String eventName = null;
             if (status == iHealthDevicesManager.DEVICE_STATE_CONNECTED) {
                 eventName = Event_Device_Connected;
@@ -129,21 +131,21 @@ public class iHealthDeviceManagerModule extends iHealthBaseModule implements Lif
                 eventName = Event_Device_Disconnect;
             }
             if (eventName != null) {
-              WritableMap params = Arguments.createMap();
-              params.putString("mac", mac);
-              if (deviceType.equals("ECGUSB")) {
-                params.putString("type", "ECG3USB");
+                WritableMap params = Arguments.createMap();
+                params.putString("mac", mac);
+                if (deviceType.equals("ECGUSB")) {
+                    params.putString("type", "ECG3USB");
 
-              } else if (deviceType.equals("KN-550BT")) {
-                params.putString("type", "KN550");
+                } else if (deviceType.equals("KN-550BT")) {
+                    params.putString("type", "KN550");
 
-              } else {
-                params.putString("type", deviceType);
-              }
-              if (errorID != 0) {
-                params.putInt("errorid", errorID);
-              }
-              sendEvent(eventName, params);
+                } else {
+                    params.putString("type", deviceType);
+                }
+                if (errorID != 0) {
+                    params.putInt("errorid", errorID);
+                }
+                sendEvent(eventName, params);
             }
         }
 
@@ -166,28 +168,28 @@ public class iHealthDeviceManagerModule extends iHealthBaseModule implements Lif
 
         @Override
         public void onSDKStatus(int statusId, String statusMessage) {
-        if (iHealthDevicesManager.SDK_STATUS_BLUETOOTH_DISABLE == statusId) {
-            Log.i("", "Bluetooth service is disable!");
+            if (iHealthDevicesManager.SDK_STATUS_BLUETOOTH_DISABLE == statusId) {
+                Log.i("", "Bluetooth service is disable!");
 
-        } else if (iHealthDevicesManager.SDK_STATUS_LOCATION_DISABLE == statusId) {
-            Log.i("", "Location service is disable!");
+            } else if (iHealthDevicesManager.SDK_STATUS_LOCATION_DISABLE == statusId) {
+                Log.i("", "Location service is disable!");
 
-        } else if (iHealthDevicesManager.SDK_STATUS_BLUETOOTH_PERMISSION == statusId) {
-            Log.i("", "Miss android permission: " + statusMessage);
+            } else if (iHealthDevicesManager.SDK_STATUS_BLUETOOTH_PERMISSION == statusId) {
+                Log.i("", "Miss android permission: " + statusMessage);
 
-        } else if (iHealthDevicesManager.SDK_STATUS_LICENSE_EXPIRED == statusId) {
-            Log.i("", "License is not match with application id or is expired!");
+            } else if (iHealthDevicesManager.SDK_STATUS_LICENSE_EXPIRED == statusId) {
+                Log.i("", "License is not match with application id or is expired!");
 
-        } else if (iHealthDevicesManager.SDK_STATUS_LICENSE_DEVICE_PERMISSION == statusId) {
-            Log.i("", "Need this device permission!");
+            } else if (iHealthDevicesManager.SDK_STATUS_LICENSE_DEVICE_PERMISSION == statusId) {
+                Log.i("", "Need this device permission!");
 
+            }
         }
-    }
 
     };
 
     private void commandHandleDeviceNotify(String mac, String deviceType, String action, String message) {
-        //为了与iOS返回值保持一致，需要进行二次加工
+        // 为了与iOS返回值保持一致，需要进行二次加工
         iHealthBaseModule module = null;
         switch (deviceType) {
             case iHealthDevicesManager.TYPE_BP5:
@@ -360,7 +362,6 @@ public class iHealthDeviceManagerModule extends iHealthBaseModule implements Lif
         return modelName;
     }
 
-
     @Override
     public void onHostResume() {
         if (callbackId == 0) {
@@ -468,7 +469,7 @@ public class iHealthDeviceManagerModule extends iHealthBaseModule implements Lif
     @ReactMethod
     public void startDiscovery(String type) {
         if (type.equals("BG1")) {
-            registerReceiver();//scan BG1
+            registerReceiver();// scan BG1
         } else {
             iHealthDevicesManager.getInstance().startDiscovery(getDiscoveryType(type));
         }
@@ -497,7 +498,7 @@ public class iHealthDeviceManagerModule extends iHealthBaseModule implements Lif
 
     @ReactMethod
     public void connectTherm(String userName, String mac, String type, int unit, int measureTarget,
-                             int functionTarget, int hour, int minute, int second) {
+            int functionTarget, int hour, int minute, int second) {
         iHealthDevicesManager.getInstance().connectTherm(userName, mac, type,
                 unit, measureTarget, functionTarget, hour, minute, second);
     }
@@ -505,7 +506,8 @@ public class iHealthDeviceManagerModule extends iHealthBaseModule implements Lif
     @ReactMethod
     public void authenConfigureInfo(String userName, String clientID, String clientSecret) {
         this.userName = userName;
-        // iHealthDevicesManager.getInstance().sdkUserInAuthor(mContext, userName, clientID, clientSecret, callbackId);
+        // iHealthDevicesManager.getInstance().sdkUserInAuthor(mContext, userName,
+        // clientID, clientSecret, callbackId);
     }
 
     @ReactMethod
@@ -524,10 +526,9 @@ public class iHealthDeviceManagerModule extends iHealthBaseModule implements Lif
 
     @Override
     public void handleNotify(String mac, String deviceType, String action, String message) {
-        //Do nothing
+        // Do nothing
     }
 
-
     private void registerReceiver() {
         IntentFilter intentFilter = new IntentFilter();
         intentFilter.addAction(Intent.ACTION_HEADSET_PLUG);
@@ -571,7 +572,8 @@ public class iHealthDeviceManagerModule extends iHealthBaseModule implements Lif
                             Utils.jsonToMap(jsonObject.toString(), params);
 
                             if (intent.getExtras().getInt("state") == 1) {
-                                if (intent.getExtras().getInt("microphone") == 1) {//1 if headset has a microphone, 0 otherwise
+                                if (intent.getExtras().getInt("microphone") == 1) {// 1 if headset has a microphone, 0
+                                                                                   // otherwise
                                     sendEvent(Event_Scan_Device, params);
                                 } else {
                                     Log.e(TAG, "headSet has no microphone");
@@ -589,7 +591,8 @@ public class iHealthDeviceManagerModule extends iHealthBaseModule implements Lif
                         case Bg1Profile.ACTION_BG1_CONNECT_RESULT:
                             int connectFlag = intent.getIntExtra(Bg1Profile.BG1_CONNECT_RESULT, -1);
                             jsonObject.put(Bg1Profile.BG1_CONNECT_RESULT, connectFlag);
-                            jsonObject.put(DESCRIPTION, getErrorDescription(Bg1Profile.ACTION_BG1_CONNECT_RESULT, connectFlag));
+                            jsonObject.put(DESCRIPTION,
+                                    getErrorDescription(Bg1Profile.ACTION_BG1_CONNECT_RESULT, connectFlag));
                             if (formatIdps(idps) != null) {
                                 jsonObject.put(IDPS, formatIdps(idps));
                             }
@@ -603,15 +606,17 @@ public class iHealthDeviceManagerModule extends iHealthBaseModule implements Lif
                         case Bg1Profile.ACTION_BG1_SENDCODE_RESULT:
                             int sendCodeFlag = intent.getIntExtra(Bg1Profile.BG1_SENDCODE_RESULT, -1);
                             jsonObject.put(Bg1Profile.BG1_SENDCODE_RESULT, sendCodeFlag);
-                            jsonObject.put(DESCRIPTION, getErrorDescription(Bg1Profile.ACTION_BG1_SENDCODE_RESULT, sendCodeFlag));
-                            //Utils.jsonToMap(jsonObject.toString(), params);
+                            jsonObject.put(DESCRIPTION,
+                                    getErrorDescription(Bg1Profile.ACTION_BG1_SENDCODE_RESULT, sendCodeFlag));
+                            // Utils.jsonToMap(jsonObject.toString(), params);
 
                             module.handleNotify(mac, BG1, Bg1Profile.ACTION_BG1_SENDCODE_RESULT, jsonObject.toString());
                             break;
                         case Bg1Profile.ACTION_BG1_MEASURE_ERROR:
                             int errorNumber = intent.getIntExtra(Bg1Profile.BG1_MEASURE_ERROR, -1);
                             jsonObject.put(Bg1Profile.BG1_MEASURE_ERROR, errorNumber);
-                            jsonObject.put(DESCRIPTION, getErrorDescription(Bg1Profile.ACTION_BG1_MEASURE_ERROR, errorNumber));
+                            jsonObject.put(DESCRIPTION,
+                                    getErrorDescription(Bg1Profile.ACTION_BG1_MEASURE_ERROR, errorNumber));
                             module.handleNotify(mac, BG1, Bg1Profile.ACTION_BG1_MEASURE_ERROR, jsonObject.toString());
                             break;
 
@@ -623,7 +628,8 @@ public class iHealthDeviceManagerModule extends iHealthBaseModule implements Lif
                         case Bg1Profile.ACTION_BG1_IDPS:
                             idps = intent.getStringExtra(Bg1Profile.BG1_IDPS);
                             mac = getMacFromIdps(idps);
-                            // module.handleNotify(mac, BG1, Bg1Profile.ACTION_BG1_IDPS, intent.getStringExtra(Bg1Profile.BG1_IDPS));
+                            // module.handleNotify(mac, BG1, Bg1Profile.ACTION_BG1_IDPS,
+                            // intent.getStringExtra(Bg1Profile.BG1_IDPS));
                             break;
                         default:
                             module.handleNotify(mac, BG1, intent.getAction(), jsonObject.toString());
@@ -637,7 +643,7 @@ public class iHealthDeviceManagerModule extends iHealthBaseModule implements Lif
         }
 
         String getMacFromIdps(String idps) {
-            //{"IDPS":[{"DeviceId":"9D300A5682","FirmWare":"13.5.0","HardWare":"13.6.0"}]}
+            // {"IDPS":[{"DeviceId":"9D300A5682","FirmWare":"13.5.0","HardWare":"13.6.0"}]}
             try {
                 JSONObject sourceObject = new JSONObject(idps);
                 JSONArray array = sourceObject.getJSONArray("IDPS");
@@ -660,7 +666,6 @@ public class iHealthDeviceManagerModule extends iHealthBaseModule implements Lif
             return null;
         }
 
-
         String getErrorDescription(String action, int err_num) {
             switch (action) {
                 case Bg1Profile.ACTION_BG1_CONNECT_RESULT:
@@ -674,12 +679,12 @@ public class iHealthDeviceManagerModule extends iHealthBaseModule implements Lif
                         case 1:
                             return "Hand shake timeout.";
                         case 2:
-                        case 16://1307
+                        case 16:// 1307
                             return "Get idps failed.";
                         case 3:
                             return "Register clientId failed.";
                         case 5:
-                        case 17://1307
+                        case 17:// 1307
                             return "Identify failed.";
                         case 32:
                             return "Connect timeout";
@@ -741,6 +746,7 @@ public class iHealthDeviceManagerModule extends iHealthBaseModule implements Lif
     @ReactMethod
     public void sdkAuthWithLicense(String license) {
         boolean result = false;
+        WritableMap writableMap = Arguments.createMap();
         try {
             InputStream ins = mContext.getAssets().open(license);
             byte[] licenseBuffer = new byte[ins.available()];
@@ -748,11 +754,15 @@ public class iHealthDeviceManagerModule extends iHealthBaseModule implements Lif
             ins.close();
             result = iHealthDevicesManager.getInstance().sdkAuthWithLicense(licenseBuffer);
         } catch (FileNotFoundException e) {
+            writableMap.putString("error", e.getMessage());
             e.printStackTrace();
         } catch (IOException e) {
+            writableMap.putString("error", e.getMessage());
+            e.printStackTrace();
+        } catch (Exception e) {
+            writableMap.putString("error", e.getMessage());
             e.printStackTrace();
         }
-        WritableMap writableMap = Arguments.createMap();
         writableMap.putBoolean("access", result);
         sendEvent(Event_Authenticate_Result, writableMap);
     }
diff --git a/node_modules/@ihealth/ihealthlibrary-react-native/module/iHealthDeviceManagerModule.js b/node_modules/@ihealth/ihealthlibrary-react-native/module/iHealthDeviceManagerModule.js
index 493749d..6fa9a99 100644
--- a/node_modules/@ihealth/ihealthlibrary-react-native/module/iHealthDeviceManagerModule.js
+++ b/node_modules/@ihealth/ihealthlibrary-react-native/module/iHealthDeviceManagerModule.js
@@ -5,6 +5,10 @@ var {NativeModules, Platform} = require('react-native');
 
 var RCTModule = NativeModules.iHealthDeviceManagerModule
 
+console.log(NativeModules);
+console.log(NativeModules.iHealthDeviceManagerModule);
+console.log(RCTModule);
+
 /**
  * @module iHealthDeviceManagerModule
  */
