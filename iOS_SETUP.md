# iOS Setup Guide for FHIR Mobile MVP

This comprehensive guide will help you set up, build, and run the iOS version of the FHIR Mobile MVP application, including all necessary configurations for medical device integrations (particularly iHealth SDK).

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Development Environment Setup](#development-environment-setup)
3. [Project Configuration](#project-configuration)
4. [iHealth SDK Configuration](#ihealth-sdk-configuration)
5. [Building and Running](#building-and-running)
6. [Troubleshooting](#troubleshooting)
7. [Advanced Configuration](#advanced-configuration)

## Prerequisites

### System Requirements

- **macOS**: macOS 12.0 (Monterey) or later
- **Xcode**: Version 14.0 or later
- **iOS Deployment Target**: iOS 12.0 or later
- **Node.js**: Version 18.0 or later
- **npm/yarn**: Latest stable version

### Apple Developer Account

- Apple Developer Account (free or paid)
- For device testing and App Store distribution, a paid account ($99/year) is required

## Development Environment Setup

### 1. Install Xcode

```bash
# Install Xcode from the Mac App Store or Apple Developer Portal
# After installation, install command line tools:
xcode-select --install
```

### 2. Install Node.js and Package Manager

```bash
# Using Homebrew (recommended)
brew install node
brew install yarn  # Optional, but recommended

# Verify installation
node --version
npm --version
```

### 3. Install Expo CLI and Development Tools

```bash
# Install Expo CLI globally
npm install -g @expo/cli

# Install iOS Simulator (if not already installed with Xcode)
# This is included with Xcode installation
```

### 4. Install CocoaPods

```bash
# Install CocoaPods for iOS dependency management
sudo gem install cocoapods

# Verify installation
pod --version
```

## Project Configuration

### 1. Clone and Setup Project

```bash
# Navigate to your project directory
cd /path/to/fhir-mobile-mvp

# Install dependencies
npm install
# or
yarn install
```

### 2. iOS-Specific Configuration

The project includes an iOS configuration plugin (`plugins/withIOSConfiguration.js`) that automatically handles:

- iOS permissions for Bluetooth, Location, Camera, and HealthKit
- Background modes for continuous health monitoring
- iHealth SDK license file management
- App Transport Security settings
- iOS entitlements configuration

### 3. Configure app.json

Ensure your `app.json` includes the iOS configuration plugin:

```json
{
  "expo": {
    "plugins": [
      // ... other plugins
      "./plugins/withIOSConfiguration"
    ]
  }
}
```

### 4. Install iOS Dependencies

```bash
# Navigate to iOS directory and install pods
cd ios
pod install
cd ..
```

## iHealth SDK Configuration

### 1. Obtain iHealth License

1. Create a developer account at [dev.ihealthlabs.com](https://dev.ihealthlabs.com)
2. Register your iOS app with bundle identifier: `com.anonymous.fhirmobilemvp`
3. Download the iOS license file (`.pem` format)

### 2. License File Placement

Place your iHealth iOS license file in one of these locations:

```
# Option 1: Project root (recommended)
./com_anonymous_fhirmobilemvp_ios.pem

# Option 2: iOS app bundle directory
./ios/fhirmobilemvp/com_anonymous_fhirmobilemvp_ios.pem
```

The iOS configuration plugin will automatically detect and copy the license file to the correct location.

### 3. Verify iHealth SDK Integration

The project uses `@ihealth/ihealthlibrary-react-native` version 1.8.0. Verify it's installed:

```bash
npm list @ihealth/ihealthlibrary-react-native
```

## Building and Running

### 1. Development Build (Physical Device)

```bash
# Run on connected iOS device
npx expo run:ios --device

# Or specify a specific device
npx expo run:ios --device "Your iPhone Name"
```

## Troubleshooting

### Common Issues and Solutions

#### 1. CocoaPods Installation Issues

```bash
# If you encounter permission issues
sudo gem install cocoapods

# If you have Ruby version conflicts
brew install ruby
gem install cocoapods

# Clear CocoaPods cache
pod cache clean --all
cd ios && pod deintegrate && pod install
```

#### 2. Xcode Build Errors

**Error: "No such module 'ExpoModulesCore'"**
```bash
cd ios
pod install
```

**Error: Code signing issues**
1. Open `ios/fhirmobilemvp.xcworkspace` in Xcode
2. Select your project in the navigator
3. Go to "Signing & Capabilities"
4. Select your development team
5. Ensure bundle identifier matches your Apple Developer account

#### 3. iHealth SDK Issues

**Error: "iHealth license not found"**
- Ensure license file is in the correct location
- Check file name matches expected format
- Verify file permissions are readable

**Error: "iHealth authentication failed"**
- Verify license file is valid and not corrupted
- Ensure bundle identifier matches the license
- Check that you're using the correct iOS license (not Android)

#### 4. Bluetooth Permission Issues

**Error: "Bluetooth permission denied"**
- Ensure Info.plist contains proper usage descriptions
- Check that the iOS configuration plugin is applied
- Verify app has requested permissions at runtime

#### 5. Build Performance Issues

```bash
# Clear all caches
npx expo start --clear

# Clear iOS build cache
cd ios
rm -rf build/
pod cache clean --all
pod install

# Clear npm/yarn cache
npm cache clean --force
# or
yarn cache clean
```

### Device-Specific Troubleshooting

#### Physical Device Testing

1. **Enable Developer Mode** (iOS 16+):
   - Settings > Privacy & Security > Developer Mode > Enable

2. **Trust Developer Certificate**:
   - Settings > General > VPN & Device Management > Trust your certificate

3. **iHealth Device Connection**:
   - Ensure iHealth MyVitals app is closed
   - iHealth devices can only connect to one app at a time
   - Reset Bluetooth if connection fails

## Advanced Configuration

### Custom iOS Build Settings

To modify advanced iOS build settings, you can extend the `withIOSConfiguration` plugin:

```javascript
// In plugins/withIOSConfiguration.js
const withCustomBuildSettings = (config) => {
  return withDangerousMod(config, [
    "ios",
    async (config) => {
      // Add custom Xcode project modifications here
      return config;
    },
  ]);
};
```