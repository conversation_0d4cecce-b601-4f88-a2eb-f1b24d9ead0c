/**
 * iOS Setup Verification Script
 *
 * This script verifies that the iOS development environment is properly configured
 * for the FHIR Mobile MVP project, including all necessary tools, dependencies,
 * and configurations.
 */

const fs = require("fs");
const path = require("path");
const { execSync } = require("child_process");

// ANSI color codes for console output
const colors = {
   green: "\x1b[32m",
   red: "\x1b[31m",
   yellow: "\x1b[33m",
   blue: "\x1b[34m",
   reset: "\x1b[0m",
   bold: "\x1b[1m",
};

// Helper functions
const log = (message, color = colors.reset) => {
   console.log(`${color}${message}${colors.reset}`);
};

const success = (message) => log(`✅ ${message}`, colors.green);
const error = (message) => log(`❌ ${message}`, colors.red);
const warning = (message) => log(`⚠️  ${message}`, colors.yellow);
const info = (message) => log(`ℹ️  ${message}`, colors.blue);

const runCommand = (command, silent = true) => {
   try {
      const result = execSync(command, {
         encoding: "utf8",
         stdio: silent ? "pipe" : "inherit",
      });
      return { success: true, output: result.trim() };
   } catch (err) {
      return { success: false, error: err.message };
   }
};

const checkFileExists = (filePath) => {
   return fs.existsSync(filePath);
};

// Verification functions
const checkSystemRequirements = () => {
   log("\n📋 Checking System Requirements...", colors.bold);

   // Check macOS
   const osResult = runCommand("sw_vers -productName");
   if (osResult.success && osResult.output.includes("macOS")) {
      success("macOS detected");
   } else {
      error("macOS is required for iOS development");
      return false;
   }

   // Check Xcode
   const xcodeResult = runCommand("xcode-select -p");
   if (xcodeResult.success) {
      success("Xcode command line tools installed");

      // Check Xcode version
      const xcodeVersionResult = runCommand("xcodebuild -version");
      if (xcodeVersionResult.success) {
         const version = xcodeVersionResult.output.split("\n")[0];
         info(`${version}`);
      }
   } else {
      error("Xcode command line tools not installed");
      info("Run: xcode-select --install");
      return false;
   }

   return true;
};

const checkNodeEnvironment = () => {
   log("\n🟢 Checking Node.js Environment...", colors.bold);

   // Check Node.js
   const nodeResult = runCommand("node --version");
   if (nodeResult.success) {
      const version = nodeResult.output;
      const majorVersion = parseInt(version.substring(1).split(".")[0]);
      if (majorVersion >= 18) {
         success(`Node.js ${version} (✓ >= 18.0)`);
      } else {
         warning(`Node.js ${version} (recommended: >= 18.0)`);
      }
   } else {
      error("Node.js not installed");
      return false;
   }

   // Check npm
   const npmResult = runCommand("npm --version");
   if (npmResult.success) {
      success(`npm ${npmResult.output}`);
   } else {
      error("npm not available");
      return false;
   }

   // Check Expo CLI
   const expoResult = runCommand("npx expo --version");
   if (expoResult.success) {
      success(`Expo CLI ${expoResult.output}`);
   } else {
      warning("Expo CLI not found globally");
      info("Install with: npm install -g @expo/cli");
   }

   return true;
};

const checkCocoaPods = () => {
   log("\n🍫 Checking CocoaPods...", colors.bold);

   const podResult = runCommand("pod --version");
   if (podResult.success) {
      success(`CocoaPods ${podResult.output}`);
      return true;
   } else {
      error("CocoaPods not installed");
      info("Install with: sudo gem install cocoapods");
      return false;
   }
};

const checkProjectStructure = () => {
   log("\n📁 Checking Project Structure...", colors.bold);

   const requiredFiles = [
      "package.json",
      "app.json",
      "ios/Podfile",
      "plugins/withIOSConfiguration.js",
   ];

   let allFilesExist = true;

   requiredFiles.forEach((file) => {
      if (checkFileExists(file)) {
         success(`${file} exists`);
      } else {
         error(`${file} missing`);
         allFilesExist = false;
      }
   });

   return allFilesExist;
};

const checkiHealthLicense = () => {
   log("\n🏥 Checking iHealth License...", colors.bold);

   const possibleLicenseFiles = [
      "com_anonymous_fhirmobilemvp_ios.pem",
      "com_anonymous_fhir-mobile-mvp_ios.pem",
      "license.pem",
   ];

   const possibleLocations = [".", "ios/fhirmobilemvp"];

   let licenseFound = false;

   for (const location of possibleLocations) {
      for (const filename of possibleLicenseFiles) {
         const fullPath = path.join(location, filename);
         if (checkFileExists(fullPath)) {
            success(`iHealth iOS license found: ${fullPath}`);
            licenseFound = true;
            break;
         }
      }
      if (licenseFound) break;
   }

   if (!licenseFound) {
      warning("iHealth iOS license file not found");
      info("Download from: https://dev.ihealthlabs.com");
      info("Expected filenames: com_anonymous_fhirmobilemvp_ios.pem");
   }

   return licenseFound;
};

const checkDependencies = () => {
   log("\n📦 Checking Dependencies...", colors.bold);

   // Check if node_modules exists
   if (!checkFileExists("node_modules")) {
      error("node_modules not found");
      info("Run: npm install");
      return false;
   }

   success("node_modules directory exists");

   // Check key dependencies
   const keyDependencies = [
      "@ihealth/ihealthlibrary-react-native",
      "expo",
      "react-native",
      "react-native-ble-plx",
   ];

   let allDepsExist = true;

   keyDependencies.forEach((dep) => {
      const depPath = path.join("node_modules", dep);
      if (checkFileExists(depPath)) {
         success(`${dep} installed`);
      } else {
         error(`${dep} not installed`);
         allDepsExist = false;
      }
   });

   return allDepsExist;
};

const checkiOSConfiguration = () => {
   log("\n🍎 Checking iOS Configuration...", colors.bold);

   // Check if iOS directory exists
   if (!checkFileExists("ios")) {
      error("iOS directory not found");
      info("Run: npx expo prebuild");
      return false;
   }

   success("iOS directory exists");

   // Check Podfile.lock
   if (checkFileExists("ios/Podfile.lock")) {
      success("Podfile.lock exists (pods installed)");
   } else {
      warning("Podfile.lock not found");
      info("Run: cd ios && pod install");
   }

   // Check Xcode workspace
   if (checkFileExists("ios/fhirmobilemvp.xcworkspace")) {
      success("Xcode workspace exists");
   } else {
      error("Xcode workspace not found");
      info("Run: cd ios && pod install");
      return false;
   }

   return true;
};

const provideSummaryAndRecommendations = (results) => {
   log("\n📊 Summary", colors.bold);

   const passed = results.filter((r) => r.passed).length;
   const total = results.length;

   if (passed === total) {
      success(`All checks passed! (${passed}/${total})`);
      log(
         "\n🚀 Your iOS development environment is ready!",
         colors.green + colors.bold,
      );
      log("\nNext steps:", colors.bold);
      info("1. Run: npm run ios");
      info("2. Test on iOS simulator or device");
      info("3. Verify iHealth device connectivity");
   } else {
      warning(`${passed}/${total} checks passed`);
      log(
         "\n🔧 Please address the issues above before proceeding.",
         colors.yellow + colors.bold,
      );

      log("\nQuick fixes:", colors.bold);
      if (!results.find((r) => r.name === "dependencies").passed) {
         info("• Run: npm install");
      }
      if (!results.find((r) => r.name === "cocoapods").passed) {
         info("• Run: sudo gem install cocoapods");
      }
      if (!results.find((r) => r.name === "ios-config").passed) {
         info("• Run: npx expo prebuild && cd ios && pod install");
      }
   }
};

// Main execution
const main = () => {
   log(
      "🔍 iOS Setup Verification for FHIR Mobile MVP",
      colors.blue + colors.bold,
   );
   log("================================================", colors.blue);

   const checks = [
      { name: "system", fn: checkSystemRequirements },
      { name: "node", fn: checkNodeEnvironment },
      { name: "cocoapods", fn: checkCocoaPods },
      { name: "project", fn: checkProjectStructure },
      { name: "ihealth", fn: checkiHealthLicense },
      { name: "dependencies", fn: checkDependencies },
      { name: "ios-config", fn: checkiOSConfiguration },
   ];

   const results = checks.map((check) => ({
      name: check.name,
      passed: check.fn(),
   }));

   provideSummaryAndRecommendations(results);
};

// Run the verification
if (require.main === module) {
   main();
}

module.exports = { main };
