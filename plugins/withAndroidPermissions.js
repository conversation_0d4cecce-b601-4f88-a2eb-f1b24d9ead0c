const { withAndroidManifest } = require("@expo/config-plugins");

const withLocationPermissions = (config) => {
   return withAndroidManifest(config, (config) => {
      const permissions = config.modResults.manifest["uses-permission"] ?? [];

      const permissionsToAdd = [
         "android.permission.ACCESS_FINE_LOCATION",
         "android.permission.ACCESS_COARSE_LOCATION",
         "android.permission.ACCESS_NETWORK_STATE",
         "android.permission.ACCESS_WIFI_STATE",
         "android.permission.RECORD_AUDIO",
         "android.permission.WRITE_EXTERNAL_STORAGE",
      ];

      for (const permission of permissionsToAdd) {
         const alreadyPresent = permissions.some(
            (p) => p.$["android:name"] === permission,
         );
         if (!alreadyPresent) {
            permissions.push({
               $: { "android:name": permission },
            });
         }
      }

      config.modResults.manifest["uses-permission"] = permissions;

      return config;
   });
};

module.exports = withLocationPermissions;
