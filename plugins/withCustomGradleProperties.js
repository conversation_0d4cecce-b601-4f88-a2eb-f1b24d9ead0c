const { withGradleProperties } = require("@expo/config-plugins");

const withCustomGradleProperties = (config) => {
   return withGradleProperties(config, (config) => {
      config.modResults.push(
         { type: "property", key: "android.useAndroidX", value: "true" },
         { type: "property", key: "android.enableJetifier", value: "true" },
      );
      return config;
   });
};

module.exports = withCustomGradleProperties;
