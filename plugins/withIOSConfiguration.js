const fs = require("fs");
const path = require("path");
const {
   withInfoPlist,
   withEntitlementsPlist,
   withDangerousMod,
} = require("@expo/config-plugins");

/**
 * iOS Configuration Plugin for FHIR Mobile MVP
 *
 * This plugin handles iOS-specific configurations including:
 * - iOS permissions and usage descriptions
 * - iHealth SDK license file management
 * - iOS build settings and entitlements
 * - Background modes and capabilities
 */

const withIOSPermissions = (config) => {
   return withInfoPlist(config, (config) => {
      const infoPlist = config.modResults;

      // Bluetooth permissions for iHealth devices
      infoPlist.NSBluetoothAlwaysUsageDescription =
         "This app uses Bluetooth to connect to iHealth medical devices for health monitoring.";
      infoPlist.NSBluetoothPeripheralUsageDescription =
         "This app uses Bluetooth to connect to iHealth medical devices for health monitoring.";

      // Location permissions (required for Bluetooth scanning on iOS)
      infoPlist.NSLocationWhenInUseUsageDescription =
         "This app needs location access to scan for nearby Bluetooth devices.";
      infoPlist.NSLocationAlwaysAndWhenInUseUsageDescription =
         "This app needs location access to scan for nearby Bluetooth devices.";

      // Camera permission (if needed for QR code scanning or device setup)
      infoPlist.NSCameraUsageDescription =
         "This app may use the camera to scan QR codes for device setup.";

      // Health data permissions (for HealthKit integration if needed)
      infoPlist.NSHealthShareUsageDescription =
         "This app reads health data to provide comprehensive health monitoring.";
      infoPlist.NSHealthUpdateUsageDescription =
         "This app writes health data from connected medical devices.";

      // Background modes for continuous health monitoring
      if (!infoPlist.UIBackgroundModes) {
         infoPlist.UIBackgroundModes = [];
      }

      const backgroundModes = [
         "bluetooth-central",
         "bluetooth-peripheral",
         "background-processing",
      ];

      backgroundModes.forEach((mode) => {
         if (!infoPlist.UIBackgroundModes.includes(mode)) {
            infoPlist.UIBackgroundModes.push(mode);
         }
      });

      // iOS deployment target and device requirements
      infoPlist.LSMinimumSystemVersion = "12.0";
      infoPlist.UIRequiredDeviceCapabilities = ["arm64"];

      // App Transport Security settings for FHIR API communication
      if (!infoPlist.NSAppTransportSecurity) {
         infoPlist.NSAppTransportSecurity = {};
      }
      infoPlist.NSAppTransportSecurity.NSAllowsArbitraryLoads = false;
      infoPlist.NSAppTransportSecurity.NSAllowsLocalNetworking = true;

      // Support for universal links and deep linking
      if (!infoPlist.CFBundleURLTypes) {
         infoPlist.CFBundleURLTypes = [];
      }

      return config;
   });
};

const withIOSEntitlements = (config) => {
   return withEntitlementsPlist(config, (config) => {
      const entitlements = config.modResults;

      // HealthKit entitlements
      entitlements["com.apple.developer.healthkit"] = true;
      entitlements["com.apple.developer.healthkit.access"] = [];

      // Background modes entitlements
      entitlements["com.apple.developer.background-modes"] = [
         "bluetooth-central",
         "bluetooth-peripheral",
         "background-processing",
      ];

      // Associated domains for universal links (if needed)
      // entitlements["com.apple.developer.associated-domains"] = ["applinks:yourdomain.com"];

      return config;
   });
};

const withIHealthIOSLicense = (config) => {
   return withDangerousMod(config, [
      "ios",
      async (config) => {
         const projectRoot = config._internal.projectRoot;

         // Look for iOS license files in various locations
         const possibleLicenseFiles = [
            "com_anonymous_fhirmobilemvp_ios.pem",
            "com_anonymous_fhir-mobile-mvp_ios.pem",
            "license.pem",
         ];

         const possibleSourcePaths = [
            projectRoot,
            path.join(projectRoot, "ios"),
            path.join(projectRoot, "ios", "fhirmobilemvp"),
         ];

         let sourceLicensePath = null;
         let licenseFileName = null;

         // Find the license file
         for (const fileName of possibleLicenseFiles) {
            for (const sourcePath of possibleSourcePaths) {
               const fullPath = path.join(sourcePath, fileName);
               if (fs.existsSync(fullPath)) {
                  sourceLicensePath = fullPath;
                  licenseFileName = fileName;
                  break;
               }
            }
            if (sourceLicensePath) break;
         }

         if (!sourceLicensePath) {
            console.warn(
               "⚠️  iOS iHealth license file not found. Please ensure you have:",
            );
            console.warn("   - com_anonymous_fhirmobilemvp_ios.pem");
            console.warn("   - or com_anonymous_fhir-mobile-mvp_ios.pem");
            console.warn("   - or license.pem");
            console.warn(
               "   in the project root or ios/fhirmobilemvp directory",
            );
            return config;
         }

         // Target path in iOS bundle
         const targetPath = path.join(
            projectRoot,
            "ios",
            "fhirmobilemvp",
            licenseFileName,
         );

         // Copy license file if it doesn't exist in target location
         if (!fs.existsSync(targetPath) || sourceLicensePath !== targetPath) {
            try {
               fs.copyFileSync(sourceLicensePath, targetPath);
               console.log(
                  `✅ Copied iOS iHealth license: ${licenseFileName} -> ios/fhirmobilemvp/`,
               );
            } catch (error) {
               console.error(
                  `❌ Failed to copy iOS iHealth license: ${error.message}`,
               );
            }
         } else {
            console.log(
               `✅ iOS iHealth license already in place: ${licenseFileName}`,
            );
         }

         return config;
      },
   ]);
};

const withIOSBuildSettings = (config) => {
   return withDangerousMod(config, [
      "ios",
      async (config) => {
         // This could be extended to modify Xcode project settings if needed
         // For now, we'll rely on the Podfile and Info.plist configurations

         console.log("✅ iOS build settings configured");
         return config;
      },
   ]);
};

/**
 * Main iOS Configuration Plugin
 * Combines all iOS-specific configurations
 */
const withIOSConfiguration = (config) => {
   // Apply all iOS configurations
   config = withIOSPermissions(config);
   config = withIOSEntitlements(config);
   config = withIHealthIOSLicense(config);
   config = withIOSBuildSettings(config);

   return config;
};

module.exports = withIOSConfiguration;
