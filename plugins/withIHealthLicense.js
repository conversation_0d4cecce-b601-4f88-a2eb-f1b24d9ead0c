const fs = require("fs");
const path = require("path");
const { withDangerousMod } = require("@expo/config-plugins");

const pemFilename = "com_anonymous_fhirmobilemvp_android.pem";

const withIHealthLicensePlugin = (config) => {
   return withDangerousMod(config, [
      "android",
      async (config) => {
         const projectRoot = config._internal.projectRoot;

         const sourcePemPath = path.join(projectRoot, pemFilename);
         const assetsPath = path.join(
            projectRoot,
            "android",
            "app",
            "src",
            "main",
            "assets",
         );
         const targetPemPath = path.join(assetsPath, pemFilename);

         if (!fs.existsSync(sourcePemPath)) {
            console.warn(`PEM license file not found at ${sourcePemPath}`);
            return config;
         }

         if (!fs.existsSync(assetsPath)) {
            fs.mkdirSync(assetsPath, { recursive: true });
         }

         fs.copyFileSync(sourcePemPath, targetPemPath);
         console.log(`Copied license to ${targetPemPath}`);

         return config;
      },
   ]);
};

module.exports = withIHealthLicensePlugin;
