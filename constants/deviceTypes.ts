export interface DeviceTypeConfig {
   id: string;
   name: string;
   description: string;
   icon: string;
   sdkDeviceType: string;
   color: string;
   category: "pulse-oximeter" | "scale" | "blood-pressure" | "thermometer";
}

export const SUPPORTED_DEVICE_TYPES: DeviceTypeConfig[] = [
   {
      id: "po3",
      name: "Pulse Oximeter Air",
      description: "Monitor blood oxygen levels and heart rate",
      icon: "🫁",
      sdkDeviceType: "PO3",
      color: "#FF6B6B",
      category: "pulse-oximeter",
   },
   {
      id: "hs2spro",
      name: "Nexus Pro Scale",
      description: "Track weight and body composition",
      icon: "⚖️",
      sdkDeviceType: "HS2S Pro",
      color: "#4ECDC4",
      category: "scale",
   },
];

export const getDeviceTypeConfig = (
   deviceType: string,
): DeviceTypeConfig | undefined => {
   return SUPPORTED_DEVICE_TYPES.find(
      (config) =>
         config.sdkDeviceType === deviceType || config.id === deviceType,
   );
};

export const getDeviceTypeBySDKType = (
   sdkDeviceType: string,
): DeviceTypeConfig | undefined => {
   return SUPPORTED_DEVICE_TYPES.find(
      (config) => config.sdkDeviceType === sdkDeviceType,
   );
};
